"""
应用配置设置
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from pathlib import Path

# 获取backend/app目录的路径
backend_app_dir = Path(__file__).parent.parent / "app"
env_file_path = backend_app_dir / "main.env"

# 获取项目根目录路径
project_root = backend_app_dir.parent.parent

class Settings(BaseSettings):
    """应用配置类"""
    
    # OpenAI API配置
    openai_api_key: str
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-4o-mini"
    embedding_model: str = "text-embedding-3-small"
    
    # 应用配置
    app_host: str = "0.0.0.0"
    app_port: int = 9000
    data_dir: str = "./data"
    storage_dir: str = "./storage"
    collection_name: str = "documents"
    
    # ChromaDB配置
    chroma_db_impl: str = "duckdb+parquet"
    chroma_persist_directory: str = "./storage"

    # RAG配置
    similarity_top_k: int = 5
    response_mode: str = "compact"  # compact, tree_summarize, simple_summarize
    temperature: float = 0.1
    max_tokens: int = 1000
    
    # CORS配置
    allowed_origins: List[str] = [
        # 开发环境
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:9000",
        "http://127.0.0.1:9000",
        "http://localhost:8002",
        "http://127.0.0.1:8002",
        # 生产环境
        "http://************:8003",
        "https://************:8003",
        "http://www.gzmdrw.cn/sys/wwltzs/",
        "https://www.gzmdrw.cn/sys/wwltzs/",
        
    ]
    
    class Config:
        env_file = str(env_file_path)
        env_file_encoding = "utf-8"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 将相对路径转换为绝对路径（相对于项目根目录）
        self._resolve_paths()
    
    def _resolve_paths(self):
        """解析相对路径为绝对路径"""
        # 如果路径是相对路径，则相对于项目根目录解析
        if not os.path.isabs(self.data_dir):
            # 使用 resolve() 来规范化路径
            resolved_path = (project_root / self.data_dir).resolve()
            self.data_dir = str(resolved_path)
        if not os.path.isabs(self.storage_dir):
            resolved_path = (project_root / self.storage_dir).resolve()
            self.storage_dir = str(resolved_path)
        if not os.path.isabs(self.chroma_persist_directory):
            resolved_path = (project_root / self.chroma_persist_directory).resolve()
            self.chroma_persist_directory = str(resolved_path)


# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.data_dir, exist_ok=True)
os.makedirs(settings.storage_dir, exist_ok=True)
