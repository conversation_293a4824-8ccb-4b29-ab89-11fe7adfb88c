<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档管理 - RAG聊天应用</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 在HTML头部或页面开始处添加 -->
    <!-- <meta id="api-config" 
        data-api-base-url="http://*************:9001/" 
        data-main-api-url="http://*************:9000/"> -->
    <div id="api-config" 
        data-main-api-url="http://localhost:9000"
        data-api-base-url="http://localhost:9001"
        style="display:none;">
    </div>
    <style>
        .documents-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .nav-links {
            display: flex;
            gap: 15px;
        }
        
        .nav-link {
            padding: 8px 16px;
            background: #6366f1;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.2s;
        }
        
        .nav-link:hover {
            background: #4f46e5;
        }
        
        .upload-section {
            background: #f9fafb;
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            transition: border-color 0.2s;
        }
        
        .upload-section:hover {
            border-color: #6366f1;
        }
        
        .upload-section.dragover {
            border-color: #4f46e5;
            background: #eef2ff;
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #6b7280;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 1.1rem;
            color: #374151;
            margin-bottom: 20px;
        }
        
        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .upload-btn {
            background: #6366f1;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .upload-btn:hover {
            background: #4f46e5;
        }
        
        .documents-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .list-header {
            background: #f3f4f6;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .refresh-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .refresh-btn:hover {
            background: #059669;
        }
        
        .add-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-left: 10px;
        }
        
        .add-btn:hover {
            background: #059669;
        }
        
        .add-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .delete-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-left: 10px;
        }
        
        .delete-btn:hover {
            background: #dc2626;
        }
        
        .delete-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .documents-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .documents-table th,
        .documents-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .documents-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .documents-table tr:hover {
            background: #f9fafb;
        }
        
        .file-name {
            font-weight: 500;
            color: #1f2937;
        }
        
        .file-stats {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .loading i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .alert.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .alert.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .stats-bar {
            background: #f3f4f6;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #6b7280;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .table-delete-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }
        
        .table-delete-btn:hover {
            background: #dc2626;
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .divider {
            width: 1px;
            height: 24px;
            background-color: #e0e0e0;
        }
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        .search-container i {
            position: absolute;
            left: 12px;
            color: #888;
        }
        #searchInput {
            padding: 0.6rem 0.8rem 0.6rem 2.2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 0.9rem;
            width: 250px;
            transition: all 0.2s ease-in-out;
        }
        #searchInput:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
        }
        #statsBar {
            display: none;
            justify-content: space-around;
            padding: 1rem;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-bottom: 1.5rem;
        }
        .stat strong {
            font-size: 1.5rem;
            font-weight: 600;
            color: #007bff;
        }
        .controls-container {
            padding: 0 20px;
        }
        /* 弹窗遮罩 */
        .modal {
            position: fixed;
            z-index: 9999;
            left: 0; top: 0; right: 0; bottom: 0;
            background: rgba(31, 41, 55, 0.25); /* 深色半透明 */
            display: flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.2s;
        }
        /* 隐藏弹窗 */
        .modal.hidden {
            display: none;
        }
        /* 弹窗主体 */
        .modal-content {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(99,102,241,0.12);
            max-width: 480px;
            width: 90vw;
            padding: 2.2rem 2rem 1.5rem 2rem;
            position: relative;
            animation: modal-fade-in 0.2s;
        }
        /* 弹窗标题 */
        #confirmTitle {
            text-align: center;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1.2rem;
            letter-spacing: 0.02em;
        }
        /* 可选：如果不需要标题，直接隐藏 */
        .hide-modal-title {
            display: none !important;
        }
        /* 弹窗内容区 */
        #confirmContent {
            margin-bottom: 1.5rem;
        }
        /* 按钮区 */
        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding-top: 0.5rem;
            padding-bottom: 0.2rem;
            border-top: 1px solid #f3f4f6;
            margin: 0 -0.5rem -0.5rem -0.5rem;
        }
        /* 按钮样式 */
        .modal-actions .btn {
            min-width: 80px;
            padding: 0.6em 1.2em;
            border-radius: 8px;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.18s;
            margin-top: 0.5rem;
        }
        .modal-actions .btn.success {
            background: #6366f1;
            color: #fff;
            border: none;
        }
        .modal-actions .btn.success:hover {
            background: #4f46e5;
        }
        .modal-actions .btn {
            background: #f3f4f6;
            color: #374151;
        }
        .modal-actions .btn:hover {
            background: #e5e7eb;
        }
        /* 动画 */
        @keyframes modal-fade-in {
            from { opacity: 0; transform: translateY(30px);}
            to   { opacity: 1; transform: translateY(0);}
        }
    </style>
</head>
<body>
    <div class="documents-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-folder-open"></i>
                文档管理
            </h1>
            <div class="nav-links">
                <a href="/" class="nav-link">
                    <i class="fas fa-comments"></i> 返回聊天
                </a>
            </div>
        </div>

        <!-- 提示信息 -->
        <div id="alertContainer"></div>

        <!-- 文件上传区域 -->
        <div class="upload-section" id="uploadSection">
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="upload-text">
                拖拽TXT文件到此处，或点击按钮选择文件上传
                <br>
                <small>支持同名文件替换，系统会自动删除旧文件的所有数据</small>
            </div>
            <div class="file-input-wrapper">
                <input type="file" id="fileInput" class="file-input" accept=".txt" multiple>
                <button class="upload-btn">
                    <i class="fas fa-plus"></i> 选择文件
                </button>
            </div>
        </div>

        <!-- 文档列表 -->
        <div class="documents-list">
            <div class="list-header">
                <h2 class="list-title">已上传文档</h2>
            </div>
            <div id="statsBar" style="margin: 20px;">
                <div class="stat">
                    <i class="fas fa-file-alt"></i>
                    <span>文档总数</span>
                    <strong id="docCount">0</strong>
                </div>
                <div class="stat">
                    <i class="fas fa-search"></i>
                    <span>显示条数</span>
                    <strong id="displayedCount">0</strong>
                </div>
                <div class="stat">
                    <i class="fas fa-puzzle-piece"></i>
                    <span>文档块总数</span>
                    <strong id="chunkCount">0</strong>
                </div>
                <div class="stat">
                    <i class="fas fa-clock"></i>
                    <span>最后更新</span>
                    <strong id="lastUpdate">-</strong>
                </div>
            </div>
            <div class="controls-container">
                <div class="controls">
                    <button id="refreshBtn" class="btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <div class="search-container">
                        <i class="fas fa-search"></i>
                        <input type="search" id="searchInput" placeholder="按文件名搜索..." />
                    </div>
                    <div class="divider"></div>
                    <button id="addBtn" class="btn success">
                        <i class="fas fa-plus-circle"></i> 更新数据库
                    </button>
                    <button id="deleteBtn" class="btn danger">
                        <i class="fas fa-trash-alt"></i> 删除文档
                    </button>
                </div>
            </div>
            
            <div id="documentsContent">
                <div class="loading">
                    <div><i class="fas fa-spinner fa-spin"></i></div>
                    <div>加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认弹窗 -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <h3 id="confirmTitle">确认操作</h3>
            <div id="confirmContent">
                <div style="text-align:center;padding:1.5rem 1rem;">
                    <div style="font-size:2.5rem;color:#6366f1;margin-bottom:1rem;">
                        <i class="fas fa-database"></i>
                    </div>
                    <div style="font-size:1.1rem;margin-bottom:1rem;">
                        <strong>确定要更新数据库吗？</strong>
                    </div>
                    <ul style="text-align:left;display:inline-block;margin:0 auto 1rem auto;padding-left:1.2em;color:#374151;">
                        <li>从 <b>CMS</b> 同步新增/更新的文章到 <b>RAG数据库</b></li>
                        <li>可能需要几分钟时间</li>
                        <li style="color:#ef4444;"><i class="fas fa-exclamation-triangle"></i> <b>同名文章将被覆盖</b></li>
                    </ul>
                    <div style="color:#6b7280;font-size:0.95rem;">
                        操作期间请勿关闭页面
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button id="confirmCancel" class="btn">取消</button>
                <button id="confirmOk" class="btn success">确定</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/documents.js"></script>
</body>
</html>
