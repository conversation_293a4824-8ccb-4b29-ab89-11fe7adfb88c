# 数据库改动记录

## 概述

本文档记录项目数据库结构的变更历史，包括表结构修改、字段添加、索引变更等。

---

## 2024-12-XX - 添加 redirect_url 和 content_url 元数据字段

### 变更描述

为 `embedding_metadata` 表添加了两个新的元数据字段，用于存储文档的跳转链接和内容链接信息。

### 变更原因

- 支持文档的原始来源链接追踪
- 提供文档的跳转链接（如微信公众号链接）
- 提供文档的内容链接（如网站文章链接）
- 增强文档的可追溯性和用户体验

### 涉及的表

#### embedding_metadata 表

**新增字段：**
- `key = "redirect_url"`，`string_value` 存储跳转链接
- `key = "content_url"`，`string_value` 存储内容链接

**字段说明：**
- `redirect_url`：文档的跳转链接，通常指向外部平台（如微信公众号）
- `content_url`：文档的内容链接，通常指向网站内部文章页面

### 数据示例

```sql
-- embedding_metadata 表中的记录示例
INSERT INTO embedding_metadata (id, key, string_value) VALUES 
(123, 'filename', '测试文章.txt'),
(123, 'file_path', '/path/to/file.txt'),
(123, 'file_size', '1024'),
(123, 'file_modified', '1703123456.789'),
(123, 'redirect_url', 'https://mp.weixin.qq.com/s/YV-73dyE_suj1C6vjInzTA'),
(123, 'content_url', 'https://gzmdrw.cn/tupianxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/680652086091845.shtml');
```

### 代码修改

#### 1. 后端服务修改

**文件：** `backend/app/rag_service.py`

**修改内容：**
- `_process_single_file` 方法：添加 `redirect_url` 和 `content_url` 参数
- `upload_document` 方法：添加 URL 参数并传递给处理函数
- 在文档元数据中写入这两个字段

**修改前：**
```python
def _process_single_file(self, file_path: Path, custom_filename: str = None):
    # 为文档添加元数据
    for doc in documents:
        doc.metadata.update({
            "filename": filename,
            "file_path": str(file_path),
            "file_size": file_path.stat().st_size,
            "file_modified": str(file_path.stat().st_mtime)
        })
```

**修改后：**
```python
def _process_single_file(self, file_path: Path, custom_filename: str = None, redirect_url: str = None, content_url: str = None):
    # 为文档添加元数据
    for doc in documents:
        doc.metadata.update({
            "filename": filename,
            "file_path": str(file_path),
            "file_size": file_path.stat().st_size,
            "file_modified": str(file_path.stat().st_mtime),
            "redirect_url": redirect_url,
            "content_url": content_url
        })
```

#### 2. API 接口修改

**文件：** `backend/app/main.py`

**修改内容：**
- 上传文档接口添加 `redirect_url` 和 `content_url` 参数
- 使用 `Form(None)` 正确接收表单字段

**修改前：**
```python
@app.post("/api/documents/upload", response_model=UploadDocumentResponse)
async def upload_document(file: UploadFile = File(...)):
```

**修改后：**
```python
@app.post("/api/documents/upload", response_model=UploadDocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    redirect_url: str = Form(None),
    content_url: str = Form(None)
):
```

#### 3. 前端修改

**文件：** `frontend/static/js/documents.js`

**修改内容：**
- `uploadSingleFile` 方法：添加 URL 参数支持
- `addDocuments` 方法：传递文章的 URL 信息

**修改前：**
```javascript
async uploadSingleFile(file) {
    const formData = new FormData();
    formData.append("file", file);
    // ...
}
```

**修改后：**
```javascript
async uploadSingleFile(file, redirect_url = null, content_url = null) {
    const formData = new FormData();
    formData.append("file", file);
    
    // 添加 URL 参数
    if (redirect_url) {
        formData.append("redirect_url", redirect_url);
    }
    if (content_url) {
        formData.append("content_url", content_url);
    }
    // ...
}
```

### 数据来源

URL 信息来源于数据管道服务（端口9001）的 `/articles/to-add` 接口：

```json
{
    "content_id": 123,
    "title": "文章标题",
    "redirect_url": "https://mp.weixin.qq.com/s/YV-73dyE_suj1C6vjInzTA",
    "content_url": "https://gzmdrw.cn/tupianxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/680652086091845.shtml",
    "filename": "文章标题.txt"
}
```

### 数据流转

1. **数据管道服务** → `/articles/to-add` 返回文章列表，包含 URL 信息
2. **前端** → 调用 `/articles/{article_id}/txt` 获取文章内容
3. **前端** → 调用 `/api/documents/upload` 上传文档，传递 URL 参数
4. **RAG服务** → 将 URL 信息写入 `embedding_metadata` 表

### 兼容性说明

- **向后兼容**：现有文档的 `redirect_url` 和 `content_url` 字段为 `null` 或空字符串
- **向前兼容**：新文档会包含这两个字段，旧代码仍能正常工作
- **数据迁移**：无需数据迁移，新字段会在文档重新上传时自动添加

### 测试验证

创建了测试脚本 `test_redirect_content_url.py` 验证功能：

```bash
python test_redirect_content_url.py
```

测试内容包括：
- 正常 URL 写入验证
- null URL 处理验证
- 数据库记录完整性验证

### 影响范围

- **正面影响**：
  - 增强文档可追溯性
  - 提供更好的用户体验
  - 支持文档来源链接管理
  - **支持自动更新功能**：当CMS文章更新时间比RAG文档更新时，自动替换旧版本

- **无负面影响**：
  - 不影响现有功能
  - 不增加存储负担（URL 字段相对较小）
  - 不影响查询性能

### 自动更新功能

#### 工作原理

1. **时间比较**：
   - 数据管道服务比较 CMS 的 `update_time` 和 RAG 的 `file_modified`
   - 如果 CMS 时间更新，则标记为需要更新

2. **自动替换**：
   - 前端统一调用上传接口，不区分新增和更新
   - RAG 服务检测到同名文件时，自动删除旧数据
   - 重新上传新内容，包括最新的 URL 信息

3. **完整替换**：
   - 旧文件被完全替换（包括 data 目录和向量数据库）
   - 新文件包含最新的内容、时间戳和元数据

#### 更新流程

```
CMS 文章更新 → 数据管道检测 → 前端获取 → RAG 服务替换 → 完成更新
     ↓              ↓            ↓           ↓
  update_time  比较时间戳    统一上传    删除旧数据
    更新        标记更新      新内容      写入新数据
```

#### 测试验证

创建了测试脚本 `test_auto_update.py` 验证自动更新功能：

```bash
python test_auto_update.py
```

测试内容包括：
- 基本自动更新功能验证
- 数据管道集成测试
- 时间戳和元数据更新验证
- 内容更新验证

### 注意事项

1. **URL 格式**：确保传入的 URL 格式正确
2. **空值处理**：null 或空字符串会被存储为空字符串
3. **字符编码**：URL 可能包含特殊字符，确保正确编码
4. **长度限制**：URL 长度可能较长，但 ChromaDB 支持大文本字段

---

## 变更记录

| 日期 | 变更内容 | 负责人 | 状态 |
|------|----------|--------|------|
| 2024-12-XX | 添加 redirect_url 和 content_url 字段 | 开发团队 | 已完成 |

---

## 快速修复指南

### 问题描述
如果发现 `embedding_metadata` 表中的 `redirect_url` 和 `content_url` 字段为空字符串，可能是 FastAPI 接口参数接收问题。

### 解决方案

#### 1. 检查 FastAPI 接口参数声明

确保 `backend/app/main.py` 中的上传接口使用 `Form(None)` 而不是 `str = None`：

```python
from fastapi import Form

@app.post("/api/documents/upload", response_model=UploadDocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    redirect_url: str = Form(None),  # 使用 Form(None)
    content_url: str = Form(None)    # 使用 Form(None)
):
```

#### 2. 重启服务

修改后重启后端服务：

```bash
# 重启主API服务
cd backend/app
uvicorn main:app --host 0.0.0.0 --port 9000 --reload
```

#### 3. 重新测试

重新上传文档，检查数据库中是否正确存储了 URL 信息。

### 验证方法

1. **查看数据库记录**：
```sql
SELECT id, key, string_value 
FROM embedding_metadata 
WHERE key IN ('redirect_url', 'content_url') 
AND string_value != '';
```

2. **运行测试脚本**：
```bash
python test_redirect_content_url.py
```

3. **检查前端日志**：
在浏览器控制台查看上传时的日志输出，确认 URL 参数是否正确传递。

---

## 相关文档

- [数据字典.md](./数据字典.md) - 数据库表结构说明
- [API_SERVICES_README.md](../backend/app/data_pipeline/API_SERVICES_README.md) - API 服务说明
- [文档管理智能同步功能说明.md](./文档管理智能同步功能说明.md) - 文档同步功能说明 