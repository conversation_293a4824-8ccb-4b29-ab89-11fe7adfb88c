"""
RAG服务核心类
基于LlamaIndex实现混合检索（BM25 + 向量混合检索）
"""
import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import requests
import chromadb
from llama_index.core import VectorStoreIndex, StorageContext, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core import SimpleDirectoryReader
from llama_index.core.retrievers import QueryFusionRetriever
from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.response_synthesizers import CompactAndRefine
from llama_index.core.prompts import PromptTemplate
from llama_index.core.prompts.default_prompts import DEFAULT_TEXT_QA_PROMPT

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.config import settings

logger = logging.getLogger(__name__)


class RAGService:
    """RAG服务类，实现文档加载、索引构建和混合检索"""

    def __init__(self):
        self.index: Optional[VectorStoreIndex] = None
        self.query_engine = None
        self.chroma_client = None
        self.collection = None
        self.custom_qa_prompt = None
        self.custom_refine_prompt = None

        # 初始化自定义提示词模板
        self._setup_custom_prompts()
        # 初始化LlamaIndex设置
        self._setup_llama_index()
        
        # 初始化ChromaDB
        self._setup_chroma()
        
        # 加载现有索引或创建新索引
        self._load_or_create_index()

    def _setup_custom_prompts(self):
        """设置自定义提示词模板"""
        # 自定义QA提示词模板 - 针对贵阳人文科技学院优化
        qa_template = """
你是贵阳人文科技学院的智能助手"文文"，专门为师生提供准确、有用的信息服务。

请根据以下上下文信息回答用户的问题。回答时请遵循以下原则：

1. **准确性**：严格基于提供的上下文信息回答，不要编造或推测信息
2. **完整性**：尽可能提供完整、详细的答案
3. **友好性**：使用亲切、专业的语调
4. **结构化**：对于复杂信息，使用清晰的结构组织答案。
5. **学校特色**：突出贵阳人文科技学院的特色和优势

如果上下文信息不足以回答问题，请诚实说明，并建议用户联系相关部门获取更详细信息。
在主要标题（如“## 学校特色与优势”）后插入一个空行，次级标题（如“### 发展愿景”）后直接开始段落内容，列表项后不插入空行。
上下文信息：
{context_str}

用户问题：{query_str}

请提供准确、有用的回答：
"""

        # 自定义精炼提示词模板
        refine_template = """
你是贵阳人文科技学院的智能助手"文文"。现在需要根据新的上下文信息来完善之前的回答。

原始问题：{query_str}

现有回答：
{existing_answer}

新的上下文信息：
{context_msg}

请根据新的上下文信息来完善回答，确保：
1. 保持回答的准确性和完整性
2. 整合所有相关信息
3. 保持友好专业的语调
4. 突出学校特色

完善后的回答：
"""

        # 创建提示词模板对象
        self.custom_qa_prompt = PromptTemplate(qa_template)
        self.custom_refine_prompt = PromptTemplate(refine_template)

        logger.info("自定义提示词模板设置完成")

    def _setup_llama_index(self):
        """配置LlamaIndex全局设置"""
        # 强制设置环境变量
        os.environ['OPENAI_API_KEY'] = settings.openai_api_key
        os.environ['OPENAI_BASE_URL'] = settings.openai_base_url

        # 尝试monkey patch OpenAI客户端
        try:
            import openai
            # 设置全局默认值
            openai.api_key = settings.openai_api_key
            openai.base_url = settings.openai_base_url
        except Exception as e:
            logger.warning(f"设置OpenAI全局配置失败: {e}")

        # 设置LLM
        Settings.llm = OpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            model=settings.openai_model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens
        )

        # 设置嵌入模型
        Settings.embed_model = OpenAIEmbedding(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            model=settings.embedding_model
        )
        
        # 设置文本分块器
        Settings.node_parser = SentenceSplitter(
            chunk_size=512,
            chunk_overlap=50
        )
        
        logger.info("LlamaIndex设置完成")
    
    def _setup_chroma(self):
        """初始化ChromaDB客户端和集合"""
        try:
            # 创建ChromaDB客户端 - 使用新的配置方式
            self.chroma_client = chromadb.PersistentClient(
                path=settings.chroma_persist_directory
            )
            
            # 获取或创建集合
            try:
                self.collection = self.chroma_client.get_collection(
                    name=settings.collection_name
                )
                logger.info(f"加载现有集合: {settings.collection_name}")
            except Exception:
                self.collection = self.chroma_client.create_collection(
                    name=settings.collection_name
                )
                logger.info(f"创建新集合: {settings.collection_name}")
                
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            raise
    
    def _load_or_create_index(self):
        """加载现有索引或创建新索引"""
        try:
            # 创建向量存储
            vector_store = ChromaVectorStore(chroma_collection=self.collection)
            storage_context = StorageContext.from_defaults(vector_store=vector_store)

            # 总是创建新的空索引，避免从现有向量存储加载时的同步问题
            self.index = VectorStoreIndex(
                nodes=[],
                storage_context=storage_context
            )

            collection_count = self.collection.count()
            if collection_count > 0:
                logger.info(f"创建新索引，ChromaDB中有 {collection_count} 个现有文档块")
                logger.info("注意：现有的ChromaDB数据将在下次文档加载时被重新处理")
            else:
                logger.info("创建新的空索引")

            # 创建查询引擎
            self._create_query_engine()

        except Exception as e:
            logger.error(f"索引加载/创建失败: {e}")
            raise
    
    def _create_query_engine(self):
        """创建混合检索查询引擎"""
        if not self.index:
            raise ValueError("索引未初始化")

        try:
            # 创建自定义响应合成器
            from llama_index.core.response_synthesizers import get_response_synthesizer

            response_synthesizer = get_response_synthesizer(
                response_mode=settings.response_mode,
                text_qa_template=self.custom_qa_prompt,
                refine_template=self.custom_refine_prompt,
                use_async=False
            )

            # 创建查询引擎，使用自定义提示词
            self.query_engine = self.index.as_query_engine(
                similarity_top_k=settings.similarity_top_k,
                response_synthesizer=response_synthesizer,
                # 可以添加更多配置
                node_postprocessors=[],  # 可以添加后处理器
            )

            logger.info("带自定义提示词的查询引擎创建完成")

        except Exception as e:
            logger.error(f"查询引擎创建失败: {e}")
            raise

    def _get_articles_summary(self) -> Dict[str, Dict[str, str]]:
        """从data_pipeline API获取文章摘要信息，返回filename到URL的映射"""
        try:
            # 从data_pipeline API获取文章摘要
            response = requests.get("http://localhost:9001/articles/summary", timeout=10)
            response.raise_for_status()
            articles = response.json()

            # 创建filename到URL的映射
            filename_to_urls = {}
            for article in articles:
                title = article.get("title", "")
                if title:
                    filename_to_urls[title] = {
                        "redirect_url": article.get("redirect_url"),
                        "content_url": article.get("content_url")
                    }

            logger.info(f"从data_pipeline获取到 {len(filename_to_urls)} 个文章的URL信息")
            return filename_to_urls

        except Exception as e:
            logger.warning(f"获取文章摘要失败: {e}")
            return {}

    def _clean_chroma_data(self):
        """清理ChromaDB中的无效数据"""
        try:
            logger.info("开始清理ChromaDB数据...")

            # 获取所有数据
            result = self.collection.get(include=["documents", "metadatas"])

            if not result["ids"]:
                logger.warning("ChromaDB中没有数据")
                return

            # 找出无效的文档ID
            invalid_ids = []
            for i, doc_text in enumerate(result.get("documents", [])):
                if doc_text is None or (isinstance(doc_text, str) and doc_text.strip() == ""):
                    invalid_ids.append(result["ids"][i])
                    logger.warning(f"发现无效文档，ID: {result['ids'][i]}")

            # 删除无效文档
            if invalid_ids:
                self.collection.delete(ids=invalid_ids)
                logger.info(f"删除了 {len(invalid_ids)} 个无效文档")

            logger.info("ChromaDB数据清理完成")

        except Exception as e:
            logger.error(f"清理ChromaDB数据失败: {e}")
            raise

    def _rebuild_index_from_chroma(self):
        """从ChromaDB重建索引"""
        try:
            logger.info("开始从ChromaDB重建索引...")

            # 先清理无效数据
            self._clean_chroma_data()

            # 获取清理后的数据
            result = self.collection.get(include=["documents", "metadatas"])

            if not result["ids"]:
                logger.warning("ChromaDB中没有数据")
                # 创建空索引
                vector_store = ChromaVectorStore(chroma_collection=self.collection)
                storage_context = StorageContext.from_defaults(vector_store=vector_store)
                self.index = VectorStoreIndex(nodes=[], storage_context=storage_context)
                self._create_query_engine()
                return

            # 创建新的索引
            vector_store = ChromaVectorStore(chroma_collection=self.collection)
            storage_context = StorageContext.from_defaults(vector_store=vector_store)

            try:
                self.index = VectorStoreIndex.from_vector_store(
                    vector_store=vector_store,
                    storage_context=storage_context
                )
                logger.info("从向量存储成功创建索引")
            except Exception as e:
                logger.warning(f"从向量存储创建索引失败: {e}，创建空索引")
                self.index = VectorStoreIndex(nodes=[], storage_context=storage_context)

            # 重新创建查询引擎
            self._create_query_engine()

            logger.info(f"索引重建完成，文档数量: {len(result['ids'])}")

        except Exception as e:
            logger.error(f"重建索引失败: {e}")
            raise

    def load_documents(self) -> Dict[str, Any]:
        """
        加载data目录中的所有TXT文档
        实现同名文件完全替换机制
        从data_pipeline API获取URL信息
        """
        try:
            data_path = Path(settings.data_dir)
            if not data_path.exists():
                return {
                    "success": False,
                    "message": f"数据目录不存在: {data_path}",
                    "documents_processed": 0
                }

            # 读取所有TXT文件
            txt_files = list(data_path.glob("*.txt"))
            if not txt_files:
                return {
                    "success": False,
                    "message": "未找到TXT文件",
                    "documents_processed": 0
                }

            # 获取文章摘要信息（包含URL）
            filename_to_urls = self._get_articles_summary()

            processed_files = []
            replaced_files = []
            new_files = []

            for txt_file in txt_files:
                filename = txt_file.name

                # 检查是否为同名文件（需要替换）
                existing_ids = self._get_document_ids_by_filename(filename)
                if existing_ids:
                    # 删除旧文件的所有相关数据
                    self._delete_document_by_filename(filename)
                    replaced_files.append({
                        "filename": filename,
                        "old_chunks": len(existing_ids)
                    })
                    logger.info(f"删除同名文件的旧数据: {filename}, 块数: {len(existing_ids)}")
                else:
                    new_files.append(filename)

                # 获取该文件的URL信息
                url_info = filename_to_urls.get(filename, {})
                redirect_url = url_info.get("redirect_url")
                content_url = url_info.get("content_url")

                # 处理新文件，传递URL信息
                self._process_single_file(
                    txt_file,
                    redirect_url=redirect_url,
                    content_url=content_url
                )
                processed_files.append(filename)

                if redirect_url or content_url:
                    logger.info(f"为文件 {filename} 添加URL信息: redirect_url={redirect_url}, content_url={content_url}")

            # 重新创建查询引擎
            self._create_query_engine()

            # 更新replaced_files中的new_chunks信息
            for replaced_file in replaced_files:
                filename = replaced_file["filename"]
                new_ids = self._get_document_ids_by_filename(filename)
                replaced_file["new_chunks"] = len(new_ids)

            return {
                "success": True,
                "message": f"成功处理 {len(processed_files)} 个文件",
                "documents_processed": len(processed_files),
                "replaced_files": replaced_files,
                "new_files": new_files,
                "total_chunks": self.collection.count(),
                "url_info_found": len([f for f in filename_to_urls.keys() if f in [tf.name for tf in txt_files]])
            }

        except Exception as e:
            logger.error(f"文档加载失败: {e}")
            return {
                "success": False,
                "message": f"文档加载失败: {str(e)}",
                "documents_processed": 0
            }
    
    def _get_document_ids_by_filename(self, filename: str) -> List[str]:
        """根据文件名获取所有相关的文档ID"""
        try:
            result = self.collection.get(
                where={"filename": filename}
            )
            return result["ids"] if result["ids"] else []
        except Exception as e:
            logger.warning(f"查询文档ID失败: {e}")
            return []
    
    def _delete_document_by_filename(self, filename: str):
        """删除指定文件名的所有相关数据"""
        try:
            # 获取所有相关ID
            existing_ids = self._get_document_ids_by_filename(filename)
            
            if existing_ids:
                # 从ChromaDB删除
                self.collection.delete(ids=existing_ids)
                
                # 从docstore删除
                for doc_id in existing_ids:
                    if self.index.docstore.document_exists(doc_id):
                        self.index.docstore.delete_document(doc_id)
                
                logger.info(f"删除文件 {filename} 的 {len(existing_ids)} 个文档块")
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            raise
    
    def _process_single_file(self, file_path: Path, custom_filename: str = None, redirect_url: str = None, content_url: str = None):
        """处理单个文件，添加到索引中"""
        try:
            # 读取文档
            reader = SimpleDirectoryReader(
                input_files=[str(file_path)]
            )
            documents = reader.load_data()

            if not documents:
                logger.warning(f"文件为空或读取失败: {file_path}")
                return

            # 使用自定义文件名或原文件名
            filename = custom_filename or file_path.name

            # 验证和处理文档
            valid_documents = []
            for doc in documents:
                # 检查文档内容是否有效
                if doc.text is None:
                    logger.warning(f"跳过 None 文本的文档块，文件: {filename}")
                    continue
                elif not isinstance(doc.text, str):
                    logger.warning(f"跳过非字符串文本的文档块，文件: {filename}, 类型: {type(doc.text)}")
                    continue
                elif len(doc.text.strip()) == 0:
                    logger.warning(f"跳过空文本的文档块，文件: {filename}")
                    continue

                # 为文档添加元数据
                doc.metadata.update({
                    "filename": filename,
                    "file_path": str(file_path),
                    "file_size": file_path.stat().st_size,
                    "file_modified": str(file_path.stat().st_mtime),
                    "redirect_url": redirect_url,
                    "content_url": content_url
                })

                valid_documents.append(doc)

            if not valid_documents:
                logger.warning(f"文件中没有有效的文档块: {filename}")
                return

            # 添加到索引
            for doc in valid_documents:
                try:
                    self.index.insert(doc)
                    logger.debug(f"成功插入文档块到索引: {doc.metadata.get('filename', 'unknown')}")
                except Exception as e:
                    logger.error(f"插入文档块失败: {e}")
                    raise

            logger.info(f"成功处理文件: {filename}, 有效文档块: {len(valid_documents)}")

        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            raise
    
    def query(self, question: str, max_results: int = 5) -> Dict[str, Any]:
        """
        执行混合检索查询
        """
        if not self.query_engine:
            return {
                "success": False,
                "message": "查询引擎未初始化",
                "answer": "",
                "sources": []
            }
        
        try:
            # 执行查询
            response = self.query_engine.query(question)
            
            # 提取源文档信息
            sources = []
            if hasattr(response, 'source_nodes') and response.source_nodes:
                for node in response.source_nodes[:max_results]:
                    # 获取链接信息
                    redirect_url = node.metadata.get("redirect_url")
                    content_url = node.metadata.get("content_url")

                    # 优先使用 redirect_url，如果没有则使用 content_url
                    article_url = redirect_url if redirect_url else content_url

                    source_info = {
                        "filename": node.metadata.get("filename", "未知"),
                        "content": node.text[:200] + "..." if len(node.text) > 200 else node.text,
                        "score": getattr(node, 'score', 0.0)
                    }

                    # 添加链接信息（如果存在）
                    if article_url:
                        source_info["url"] = article_url
                        source_info["url_type"] = "redirect" if redirect_url else "content"

                    sources.append(source_info)
            
            return {
                "success": True,
                "answer": str(response),
                "sources": sources,
                "total_sources": len(sources)
            }
            
        except Exception as e:
            logger.error(f"查询失败: {e}")
            return {
                "success": False,
                "message": f"查询失败: {str(e)}",
                "answer": "",
                "sources": []
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            doc_count = self.collection.count() if self.collection else 0

            # 计算存储大小
            storage_path = Path(settings.chroma_persist_directory)
            storage_size = 0
            if storage_path.exists():
                for file_path in storage_path.rglob("*"):
                    if file_path.is_file():
                        storage_size += file_path.stat().st_size

            storage_size_mb = storage_size / (1024 * 1024)

            return {
                "status": "ok",
                "documents_count": doc_count,
                "storage_size": f"{storage_size_mb:.2f}MB",
                "collection_name": settings.collection_name,
                "data_directory": settings.data_dir
            }

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def get_documents_list(self) -> Dict[str, Any]:
        """获取所有文档列表"""
        try:
            if not self.collection:
                return {
                    "success": False,
                    "message": "集合未初始化",
                    "documents": [],
                    "total_chunks": 0
                }

            # 获取所有文档的元数据
            result = self.collection.get(include=["metadatas"])

            if not result["ids"]:
                return {
                    "success": True,
                    "message": "暂无文档",
                    "documents": [],
                    "total_chunks": 0
                }

            # 按文件名分组统计
            file_stats = {}
            for i, _ in enumerate(result["ids"]):
                metadata = result["metadatas"][i] if result["metadatas"] else {}
                filename = metadata.get("filename", "未知文件")

                if filename not in file_stats:
                    file_stats[filename] = {
                        "filename": filename,
                        "chunks_count": 0,
                        "file_size": metadata.get("file_size", 0),
                        "file_modified": metadata.get("file_modified", ""),
                        "file_path": metadata.get("file_path", "")
                    }

                file_stats[filename]["chunks_count"] += 1

            documents = list(file_stats.values())

            return {
                "success": True,
                "message": f"找到 {len(documents)} 个文档",
                "documents": documents,
                "total_chunks": len(result["ids"])
            }

        except Exception as e:
            logger.error(f"获取文档列表失败: {e}")
            return {
                "success": False,
                "message": f"获取文档列表失败: {str(e)}",
                "documents": [],
                "total_chunks": 0
            }

    def upload_document(self, file_content: str, filename: str, redirect_url: str = None, content_url: str = None) -> Dict[str, Any]:
        """上传单个文档"""
        try:
            # 检查文件名是否已存在
            existing_ids = self._get_document_ids_by_filename(filename)
            replaced = False
            old_chunks_count = 0

            if existing_ids:
                # 删除旧文件的所有相关数据
                old_chunks_count = len(existing_ids)
                self._delete_document_by_filename(filename)
                replaced = True
                logger.info(f"删除同名文件的旧数据: {filename}, 块数: {old_chunks_count}")

            # 确保data目录存在
            data_path = Path(settings.data_dir)
            data_path.mkdir(exist_ok=True)

            # 保存文件到data目录
            file_path = data_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)

            logger.info(f"文件已保存到: {file_path}")

            # 处理文件
            self._process_single_file(file_path, redirect_url=redirect_url, content_url=content_url)

            # 获取新的块数量
            new_ids = self._get_document_ids_by_filename(filename)
            new_chunks_count = len(new_ids)

            # 重新创建查询引擎
            self._create_query_engine()

            return {
                "success": True,
                "message": f"文档上传成功: {filename}",
                "filename": filename,
                "replaced": replaced,
                "old_chunks": old_chunks_count if replaced else 0,
                "new_chunks": new_chunks_count,
                "total_chunks": self.collection.count(),
                "file_path": str(file_path)
            }

        except Exception as e:
            logger.error(f"上传文档失败: {e}")
            return {
                "success": False,
                "message": f"上传文档失败: {str(e)}",
                "filename": filename
            }

    def delete_document(self, filename: str) -> Dict[str, Any]:
        """删除指定文档"""
        try:
            # 获取文档ID
            existing_ids = self._get_document_ids_by_filename(filename)

            if not existing_ids:
                return {
                    "success": False,
                    "message": f"文档不存在: {filename}",
                    "filename": filename
                }

            # 删除数据库中的文档
            chunks_count = len(existing_ids)
            self._delete_document_by_filename(filename)

            # 删除data目录中的文件
            data_path = Path(settings.data_dir)
            file_path = data_path / filename
            file_deleted_from_disk = False

            if file_path.exists():
                try:
                    file_path.unlink()
                    file_deleted_from_disk = True
                    logger.info(f"已从磁盘删除文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除磁盘文件失败: {e}")

            # 重新创建查询引擎
            self._create_query_engine()

            message = f"文档删除成功: {filename}"
            if file_deleted_from_disk:
                message += " (包括磁盘文件)"
            else:
                message += " (仅删除数据库记录)"

            return {
                "success": True,
                "message": message,
                "filename": filename,
                "deleted_chunks": chunks_count,
                "file_deleted_from_disk": file_deleted_from_disk,
                "total_chunks": self.collection.count()
            }

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return {
                "success": False,
                "message": f"删除文档失败: {str(e)}",
                "filename": filename
            }

