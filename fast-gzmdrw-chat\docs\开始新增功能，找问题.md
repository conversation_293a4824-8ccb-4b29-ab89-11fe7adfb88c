# 新增功能：远程拉取TXT并解压到data目录

## 功能简介

新增了一个API接口：`POST /api/fetch-remote-txts`，用于从指定远程API下载包含多个TXT文件的zip包，并自动解压所有TXT文件到本地`data`目录。该接口适合前端通过按钮触发，实现一键批量导入远程文档。

---

## 接口说明

- **接口路径**：`POST /api/fetch-remote-txts`
- **请求参数**：无（空请求体）
- **返回结果**：
  - `success`：布尔值，表示是否成功
  - `message`：操作结果描述

### 成功示例
```json
{
  "success": true,
  "message": "已成功下载并解压 10 个txt文件到 ./data 目录。"
}
```

### 失败示例
```json
{
  "detail": "下载或解压失败: 错误信息"
}
```

---

## 实现原理

1. 后端收到请求后，向远程API（如`http://************:9001/articles/`）发起GET请求，下载zip包。
2. 使用`zipfile`模块解压zip包，遍历所有文件。
3. 仅提取后缀为`.txt`的文件，写入本地`data`目录（自动创建目录）。
4. 返回成功或失败的JSON响应。

---

## 代码片段（已集成于 backend/app/main.py）

```python
@app.post("/api/fetch-remote-txts")
async def fetch_remote_txts():
    """
    从远程API下载zip包并解压所有txt到data目录
    """
    api_url = "http://************:9001/articles/"
    data_dir = "./data"
    try:
        response = requests.get(api_url)
        response.raise_for_status()
        with zipfile.ZipFile(io.BytesIO(response.content)) as zf:
            txt_count = 0
            for name in zf.namelist():
                if name.lower().endswith('.txt'):
                    with zf.open(name) as txt_file:
                        content = txt_file.read()
                        os.makedirs(data_dir, exist_ok=True)
                        filename = os.path.basename(name)
                        with open(os.path.join(data_dir, filename), 'wb') as f:
                            f.write(content)
                        txt_count += 1
        return {
            "success": True,
            "message": f"已成功下载并解压 {txt_count} 个txt文件到 {data_dir} 目录。"
        }
    except Exception as e:
        logger.error(f"下载或解压远程txt失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"下载或解压失败: {str(e)}"
        )
```

---

## 前端调用示例

```js
fetch('/api/fetch-remote-txts', { method: 'POST' })
  .then(res => res.json())
  .then(data => alert(data.message))
  .catch(err => alert('下载失败'));
```

---

## 使用建议

- 可在前端文档管理页面添加"远程拉取TXT"按钮，点击后调用该API。
- 拉取成功后可自动调用`/api/load-documents`接口，实现文档自动加载。
- 支持多次重复拉取，已存在的同名文件会被覆盖。

---

## 前端集成说明

### 1. 按钮位置

- 在文档管理页面（documents.html）文档列表头部，"刷新"按钮旁边新增"更新文档"按钮（云下载图标，id为updateDocsBtn）。

### 2. 交互流程

- 用户点击"更新文档"按钮：
  1. 按钮进入加载中状态（禁用+旋转图标）。
  2. 前端调用 `/api/fetch-remote-txts`（POST）。
  3. 成功后弹窗提示，自动刷新文档列表。
  4. 失败时弹窗报错，按钮状态恢复。

### 3. 主要前端代码片段

```js
// 绑定事件
const updateDocsBtn = document.getElementById("updateDocsBtn");
updateDocsBtn.addEventListener("click", async () => {
  updateDocsBtn.disabled = true;
  updateDocsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更新中';
  try {
    const response = await fetch("/api/fetch-remote-txts", { method: "POST" });
    const data = await response.json();
    if (data.success) {
      documentManager.showAlert(data.message, "success");
      documentManager.loadDocuments();
    } else {
      documentManager.showAlert(data.detail || data.message || "更新失败", "error");
    }
  } catch (error) {
    documentManager.showAlert("更新文档失败: " + error.message, "error");
  } finally {
    updateDocsBtn.disabled = false;
    updateDocsBtn.innerHTML = '<i class="fas fa-cloud-download-alt"></i> 更新文档';
  }
});
```

### 4. 用户体验

- 支持多次点击，自动覆盖同名文件。
- 拉取远程TXT后无需手动刷新，文档列表自动更新。
- 错误信息即时反馈。

---
