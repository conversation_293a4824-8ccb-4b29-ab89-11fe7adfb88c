---

## 问题修复：删除文档逻辑优化

### 问题描述

在删除包含大量文档块的文件时，出现 "too many SQL variables" 错误。这是因为：

1. 当文档块数量很大时，SQLite 查询中的变量数量超过了默认限制（999个）
2. 原删除逻辑试图一次性删除所有相关文档块，导致 SQL 变量过多
3. 缺乏分批处理机制，无法处理大规模文档删除

### 解决方案

#### 1. 分批删除机制

- 将删除操作分批进行，每批最多处理 100 个文档块
- 避免 SQLite 变量数量超限问题
- 提高删除操作的稳定性

#### 2. 优化删除逻辑

```python
def _delete_document_by_filename(self, filename: str):
    """删除指定文件名的所有相关数据"""
    try:
        # 先获取所有相关的文档ID
        existing_ids = self._get_document_ids_by_filename(filename)
        
        if not existing_ids:
            logger.info(f"文件 {filename} 没有找到相关文档块")
            return
        
        # 分批删除docstore中的文档（避免SQL变量过多）
        batch_size = 100  # SQLite默认限制是999个变量，我们使用100作为安全值
        for i in range(0, len(existing_ids), batch_size):
            batch_ids = existing_ids[i:i + batch_size]
            for doc_id in batch_ids:
                try:
                    if self.index.docstore.document_exists(doc_id):
                        self.index.docstore.delete_document(doc_id)
                except Exception as e:
                    logger.warning(f"删除docstore文档 {doc_id} 失败: {e}")
        
        # 使用条件删除ChromaDB中的数据（这是最安全的方式）
        try:
            self.collection.delete(where={"filename": filename})
            logger.info(f"成功删除文件 {filename} 的 {len(existing_ids)} 个文档块")
        except Exception as e:
            logger.error(f"删除ChromaDB数据失败: {e}")
            # 如果条件删除失败，尝试分批删除
            for i in range(0, len(existing_ids), batch_size):
                batch_ids = existing_ids[i:i + batch_size]
                try:
                    self.collection.delete(ids=batch_ids)
                except Exception as batch_error:
                    logger.error(f"分批删除失败 (批次 {i//batch_size + 1}): {batch_error}")
                    raise
        
    except Exception as e:
        logger.error(f"删除文档失败: {e}")
        raise
```

#### 3. 增强错误处理

- 添加详细的日志记录，便于问题排查
- 分步骤处理删除操作，确保每个步骤都有错误处理
- 提供更准确的错误信息给用户

### 改进效果

- ✅ 解决了 "too many SQL variables" 错误
- ✅ 支持删除包含大量文档块的文件
- ✅ 提高了删除操作的稳定性和可靠性
- ✅ 增强了错误处理和日志记录
- ✅ 保持了向后兼容性

--- 