# 文档管理智能同步功能说明

## 1. 功能概述

本功能实现了 CMS 与 RAG 知识库之间的文档**智能同步**，包括：
- 新增文档
- 更新（替换）文档
- 删除文档

所有操作均以"原始 txt 文件"作为唯一数据源，保证 `data` 目录与向量数据库内容一致。

---

## 2. 系统分工与接口关系

- **数据管道（data_pipeline.py）**：负责CMS数据库与RAG服务之间的同步、比对、调度。主要接口有：
  - `/articles/to-add`：返回所有需要同步到RAG的CMS文章（不再限制正文内容，空内容/仅有redirect_url的文章也会返回）。
  - `/articles/to-delete`：返回RAG有但CMS无的文档。
  - `/articles/{article_id}/txt`：导出CMS文章为txt，优先正文，无正文则用redirect_url，无内容则404。
  - `/debug/sync-status`：调试接口，显示CMS与RAG的文档数量、文件名及差异。
- **RAG服务（main.py）**：负责本地data目录与向量数据库的文档管理。主要接口有：
  - `/api/documents`：返回RAG本地所有文档信息。
  - `/api/documents/upload`：上传txt文件，保存到data目录并同步向量库。
  - `/api/documents/{filename}`：删除文档。

---

## 3. 智能同步链路与空内容处理

- `/articles/to-add` 现在会返回所有CMS文章，不管正文是否为空。
- `/articles/{article_id}/txt`：
  - 有正文内容 → 返回正文txt
  - 无正文但有redirect_url → 返回redirect_url作为txt
  - 两者都没有 → 返回404，前端不会上传无效文件
- 前端拿到txt后，调用 `/api/documents/upload` 上传，RAG服务保存到data目录并同步向量库。
- 只有上传成功，data目录才会有对应txt文件。

---

## 4. 常见同步异常与调试建议

- **RAG数据库有文档但data目录无文件**：多为RAG服务写文件异常被吞掉，需检查rag_service.py的文件写入逻辑和DATA_DIR配置。
- **/articles/to-add返回的文章无法同步**：多为正文和redirect_url都为空，/articles/{article_id}/txt会404。
- **/debug/sync-status** 可一键比对CMS与RAG的所有差异，定位缺失文件。
- **建议在RAG服务的文件写入处加详细日志，确保异常能反馈到前端。**

---

## 5. 典型API调用流程

1. 前端调用 `/articles/to-add` 获取所有待同步文章。
2. 依次请求 `/articles/{article_id}/txt` 获取txt内容。
3. 拿到内容后，调用 `/api/documents/upload` 上传。
4. 上传成功后，data目录和RAG数据库同步更新。

---

## 6. 变更记录
- 2024-06-23：首次整理，统一"新增/更新"逻辑，删除 `/articles/to-update` 接口。
- 2024-06-24：/articles/to-add 逻辑调整，所有CMS文章都可同步，空内容处理更健壮。

---

如有疑问或需扩展，请联系开发负责人。 