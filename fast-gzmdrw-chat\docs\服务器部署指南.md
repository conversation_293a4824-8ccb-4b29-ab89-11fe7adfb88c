# RAG聊天应用 - Ubuntu 22.04 服务器部署指南

## 📋 目录
1. [服务器环境准备](#1-服务器环境准备)
2. [项目文件上传](#2-项目文件上传)
3. [Python环境配置](#3-python环境配置)
4. [数据库配置说明](#4-数据库配置说明)
5. [环境变量配置](#5-环境变量配置)
6. [端口配置](#6-端口配置)
7. [Nginx配置](#7-nginx配置)
8. [进程管理配置](#8-进程管理配置)
9. [SSL证书配置](#9-ssl证书配置)
10. [启动和测试](#10-启动和测试)
11. [故障排除](#11-故障排除)
12. [维护和监控](#12-维护和监控)

## 1. 服务器环境准备

### 1.1 系统要求
- **操作系统**: Ubuntu 22.04 LTS (推荐)
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 20GB，推荐 50GB+
- **CPU**: 最少 2核，推荐 4核+

### 1.2 连接服务器
```bash
# 使用SSH连接服务器
ssh root@your_server_ip

# 或使用密钥连接
ssh -i your_key.pem ubuntu@your_server_ip
```

### 1.3 检查系统信息
```bash
# 检查系统版本
lsb_release -a

# 检查系统资源
free -h
df -h
nproc
```

### 1.4 更新系统包
```bash
# 更新包列表和系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y wget curl git vim htop tree unzip
```

### 1.5 检查并安装基础软件

**检查已安装的软件**
```bash
# 检查Python版本
python3 --version
pip3 --version

# 检查是否已安装Nginx
nginx -v 2>/dev/null && echo "Nginx已安装" || echo "Nginx未安装"

# 检查是否已安装MySQL客户端
mysql --version 2>/dev/null && echo "MySQL客户端已安装" || echo "MySQL客户端未安装"

# 检查是否已安装SQLite3
sqlite3 --version 2>/dev/null && echo "SQLite3已安装" || echo "SQLite3未安装"
```

**安装缺失的软件**
```bash
# 安装Python3和pip（如果未安装）
sudo apt install -y python3 python3-pip python3-venv python3-dev

# 安装Nginx（如果未安装）
sudo apt install -y nginx

# 安装SQLite3（如果未安装）
sudo apt install -y sqlite3 libsqlite3-dev

# 安装MySQL客户端（用于远程连接，如果未安装）
sudo apt install -y mysql-client

# 安装其他依赖
sudo apt install -y build-essential pkg-config
```

## 2. 项目文件上传

### 2.1 创建项目目录
```bash
# 创建项目根目录
sudo mkdir -p /opt/ragapp
sudo chown $USER:$USER /opt/ragapp
cd /opt/ragapp
```

### 2.2 上传项目文件
有几种方式上传项目文件：

**方式1: 使用SCP上传**
```bash
# 在本地执行，上传整个项目
scp -r /path/to/your/fast-gzmdrw-chat ubuntu@your_server_ip:/opt/ragapp/

# 或者打包后上传
tar -czf ragapp.tar.gz fast-gzmdrw-chat/
scp ragapp.tar.gz ubuntu@your_server_ip:/opt/ragapp/

# 在服务器上解压
cd /opt/ragapp
tar -xzf ragapp.tar.gz
# 注意：解压后会得到 /opt/ragapp/fast-gzmdrw-chat/ 目录
```

**方式2: 使用Git克隆**
```bash
# 如果项目在Git仓库中
cd /opt/ragapp
git clone https://github.com/your-username/your-repo.git fast-gzmdrw-chat
# 注意：克隆到指定目录名 fast-gzmdrw-chat
```

**方式3: 使用SFTP工具**
- 使用FileZilla、WinSCP等工具上传

### 2.3 设置文件权限和目录结构
```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 设置文件权限
sudo chown -R $USER:$USER .
chmod -R 755 .

# 创建必要的目录（如果不存在）
mkdir -p data storage logs

# 验证目录结构
ls -la
echo "项目目录结构："
tree -L 2 . || ls -la
```

## 3. Python环境配置

### 3.1 检查是否需要虚拟环境
```bash
# 检查系统是否有其他Python项目
ls -la /opt/
ps aux | grep python

# 检查全局Python包
pip3 list | grep -E "(fastapi|uvicorn|llama)"

# 如果服务器上有其他项目或全局包冲突，建议使用虚拟环境
echo "建议使用虚拟环境以避免包冲突"
```

### 3.2 创建Python虚拟环境（推荐）
```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 验证虚拟环境
which python
which pip
echo "虚拟环境路径: $(which python)"
```

### 3.3 升级pip和安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 如果requirements.txt不存在，手动安装核心依赖
pip install fastapi uvicorn llama-index chromadb python-multipart python-dotenv
```

### 3.4 验证安装
```bash
# 验证核心依赖
python -c "
import fastapi
import uvicorn
import llama_index
import chromadb
print('✅ 所有核心依赖安装成功')
print(f'FastAPI版本: {fastapi.__version__}')
print(f'Uvicorn版本: {uvicorn.__version__}')
"

# 检查SQLite3支持
python -c "
import sqlite3
print('✅ SQLite3支持正常')
print(f'SQLite版本: {sqlite3.sqlite_version}')
"
```

### 3.5 设置虚拟环境自动激活（可选）
```bash
# 添加到.bashrc以便自动激活
echo "source /opt/ragapp/fast-gzmdrw-chat/venv/bin/activate" >> ~/.bashrc

# 或创建快捷命令
echo "alias ragapp='cd /opt/ragapp/fast-gzmdrw-chat && source venv/bin/activate'" >> ~/.bashrc
source ~/.bashrc
```

## 4. 数据库配置说明

### 4.1 数据库架构说明

本项目使用**双数据库架构**：

1. **SQLite3** - 本地RAG服务数据库
   - 用途：存储向量数据库(ChromaDB)的元数据
   - 位置：`./storage/chroma.sqlite3`
   - 特点：无需配置，自动创建

2. **MySQL** - 远程CMS数据库连接
   - 用途：连接远程ChestnutCMS数据库
   - 配置：在`data_pipeline.env`中配置
   - 特点：仅用于数据同步，不存储本地数据

### 4.2 SQLite3配置（本地数据库）

**SQLite3无需额外配置**，系统已自动处理：

```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 验证SQLite3安装
sqlite3 --version

# 检查ChromaDB存储目录
ls -la ./storage/

# 如果目录不存在，创建它
mkdir -p ./storage
chmod 755 ./storage

# 验证目录权限
ls -la ./storage/
```

**SQLite3数据库文件说明**：
```bash
# ChromaDB会自动创建以下文件：
/opt/ragapp/fast-gzmdrw-chat/storage/
├── chroma.sqlite3          # 主数据库文件
├── chroma.sqlite3-shm      # 共享内存文件
└── chroma.sqlite3-wal      # 预写日志文件
```

### 4.3 MySQL配置（远程CMS连接）

**重要说明**：MySQL仅用于连接远程ChestnutCMS数据库，不在本地存储数据。

#### 4.3.1 安装MySQL客户端（如果未安装）
```bash
# 检查是否已安装
mysql --version

# 如果未安装，安装MySQL客户端
sudo apt install -y mysql-client
```

#### 4.3.2 测试远程MySQL连接
```bash
# 测试连接远程CMS数据库
mysql -h your_cms_host -P 3306 -u your_username -p your_database

# 测试查询CMS文章数据
mysql -h your_cms_host -P 3306 -u your_username -p -e "
USE your_database;
SELECT COUNT(*) as article_count FROM cms_content WHERE content_type='article';
SELECT title FROM cms_content WHERE content_type='article' LIMIT 5;
"
```

#### 4.3.3 data_pipeline.env MySQL配置说明

在`backend/app/data_pipeline.env`文件中配置MySQL连接：

```env
# MySQL数据库配置（连接远程ChestnutCMS）
DB_HOST=your_cms_server_ip          # CMS服务器IP地址
DB_PORT=3306                        # MySQL端口，通常是3306
DB_USER=your_cms_username           # CMS数据库用户名
DB_PASSWORD=your_cms_password       # CMS数据库密码
DB_NAME=your_cms_database           # CMS数据库名称

# 数据库连接池配置
DB_POOL_SIZE=5                      # 连接池大小
DB_MAX_OVERFLOW=10                  # 最大溢出连接数
DB_POOL_TIMEOUT=30                  # 连接超时时间(秒)
DB_POOL_RECYCLE=3600               # 连接回收时间(秒)

# 数据库连接字符串（自动生成，无需手动配置）
# DATABASE_URL=mysql+pymysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
```

#### 4.3.4 验证数据库配置
```bash
# 进入项目目录并激活虚拟环境
cd /opt/ragapp
source venv/bin/activate

# 测试数据库连接
python -c "
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('backend/app/data_pipeline.env')

# 显示数据库配置（隐藏密码）
print('数据库配置:')
print(f'主机: {os.getenv(\"DB_HOST\")}')
print(f'端口: {os.getenv(\"DB_PORT\")}')
print(f'用户: {os.getenv(\"DB_USER\")}')
print(f'数据库: {os.getenv(\"DB_NAME\")}')
print('密码: ****')
"

# 测试数据管道服务的数据库连接
cd backend/app
python -c "
from data_pipeline import engine
try:
    with engine.connect() as conn:
        result = conn.execute('SELECT 1')
        print('✅ MySQL连接测试成功')
except Exception as e:
    print(f'❌ MySQL连接失败: {e}')
"
```

### 4.4 数据库安全建议

#### 4.4.1 SQLite3安全
```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 设置适当的文件权限
chmod 640 ./storage/chroma.sqlite3
chown $USER:$USER ./storage/chroma.sqlite3

# 定期备份SQLite数据库
cp ./storage/chroma.sqlite3 ./backups/chroma_$(date +%Y%m%d_%H%M%S).sqlite3
```

#### 4.4.2 MySQL连接安全
```bash
# 确保data_pipeline.env文件权限安全
chmod 600 /opt/ragapp/fast-gzmdrw-chat/backend/app/data_pipeline.env
chown $USER:$USER /opt/ragapp/fast-gzmdrw-chat/backend/app/data_pipeline.env

# 验证文件权限
ls -la /opt/ragapp/fast-gzmdrw-chat/backend/app/data_pipeline.env
```

## 5. 环境变量配置

### 5.1 配置文件说明

本项目使用两个主要的环境配置文件：

1. **`backend/app/.env`** - RAG主服务配置
2. **`backend/app/data_pipeline.env`** - 数据管道服务配置

### 5.2 配置RAG主服务环境变量

```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 检查是否存在配置模板
ls -la backend/app/.env*

# 如果存在模板，复制它
if [ -f "backend/app/.env.template" ]; then
    cp backend/app/.env.template backend/app/.env
else
    # 创建新的配置文件
    touch backend/app/.env
fi
```

**编辑RAG主服务配置**：
```bash
vim backend/app/.env
```

**RAG主服务配置内容**：
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai-proxy.org/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=9000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB配置（SQLite3）
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://your_domain.com"]

# 日志配置
LOG_LEVEL=INFO
```

### 5.3 配置数据管道服务环境变量

```bash
# 检查是否存在配置模板
ls -la backend/app/data_pipeline*

# 如果存在示例配置，复制它
if [ -f "backend/app/data_pipeline_config_example.env" ]; then
    cp backend/app/data_pipeline_config_example.env backend/app/data_pipeline.env
else
    # 创建新的配置文件
    touch backend/app/data_pipeline.env
fi
```

**编辑数据管道服务配置**：
```bash
vim backend/app/data_pipeline.env
```

**数据管道服务配置内容**：
```env
# 数据管道API服务配置
DATA_PIPELINE_HOST=0.0.0.0
DATA_PIPELINE_PORT=9001

# MySQL数据库配置（连接远程ChestnutCMS）
DB_HOST=your_cms_server_ip
DB_PORT=3306
DB_USER=your_cms_username
DB_PASSWORD=your_cms_password
DB_NAME=your_cms_database

# 数据库连接池配置
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 网站配置
SITE_BASE_URL=https://your_domain.com

# RAG服务配置 - 指向本项目的端口9000
RAG_SERVICE_URL=http://localhost:9000

# 数据处理配置
MAX_CONTENT_LENGTH=10485760
SUPPORTED_FILE_TYPES=.txt,.html,.htm
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 安全配置
CORS_ORIGINS=["http://localhost:9000", "http://127.0.0.1:9000", "https://your_domain.com"]
RATE_LIMIT_PER_MINUTE=100
```

### 5.4 服务器端口配置（避免冲突）

**检查端口占用**：
```bash
# 检查常用端口是否被占用
netstat -tlnp | grep -E ':(9000|9001|9000|9001)'

# 如果9000/9001被占用，修改为9000/9001
```

**如果需要修改端口**，编辑配置文件：
```bash
# 修改RAG主服务端口
sed -i 's/APP_PORT=9000/APP_PORT=9000/' backend/app/.env

# 修改数据管道服务端口
sed -i 's/DATA_PIPELINE_PORT=9001/DATA_PIPELINE_PORT=9001/' backend/app/data_pipeline.env

# 同时更新RAG服务URL
sed -i 's/localhost:9000/localhost:9000/' backend/app/data_pipeline.env
```

### 5.5 创建必要目录和设置权限

```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 创建必要目录
mkdir -p logs data storage backups

# 设置目录权限
chmod 755 logs data storage backups
chown -R $USER:$USER logs data storage backups

# 设置配置文件权限（保护敏感信息）
chmod 600 backend/app/.env
chmod 600 backend/app/data_pipeline.env
chown $USER:$USER backend/app/.env
chown $USER:$USER backend/app/data_pipeline.env
```

### 5.6 验证配置

```bash
# 验证配置文件存在
ls -la backend/app/.env
ls -la backend/app/data_pipeline.env

# 验证配置文件权限
ls -la backend/app/*.env

# 测试配置加载
cd backend/app
python -c "
from dotenv import load_dotenv
import os

# 测试RAG服务配置
load_dotenv('.env')
print('RAG服务配置:')
print(f'  端口: {os.getenv(\"APP_PORT\", \"9000\")}')
print(f'  数据目录: {os.getenv(\"DATA_DIR\", \"./data\")}')
print(f'  存储目录: {os.getenv(\"STORAGE_DIR\", \"./storage\")}')

# 测试数据管道配置
load_dotenv('data_pipeline.env')
print('数据管道配置:')
print(f'  端口: {os.getenv(\"DATA_PIPELINE_PORT\", \"9001\")}')
print(f'  数据库主机: {os.getenv(\"DB_HOST\", \"未配置\")}')
print(f'  RAG服务URL: {os.getenv(\"RAG_SERVICE_URL\", \"未配置\")}')
"
```

## 6. 端口配置

### 6.1 检查端口占用情况

```bash
# 检查常用端口是否被占用
echo "检查端口占用情况..."
netstat -tlnp | grep -E ':(9000|9001|9000|9001|80|443)'

# 详细检查每个端口
for port in 9000 9001 9000 9001; do
    if netstat -tlnp | grep ":$port " > /dev/null; then
        echo "❌ 端口 $port 已被占用"
        netstat -tlnp | grep ":$port "
    else
        echo "✅ 端口 $port 可用"
    fi
done
```

### 6.2 确定最终端口配置

**推荐端口配置**：
- 如果9000/9001可用：使用默认端口
- 如果9000/9001被占用：使用9000/9001

```bash
# 检查并设置端口
cd /opt/ragapp

# 检查9000端口
if netstat -tlnp | grep ":9000 " > /dev/null; then
    echo "9000端口被占用，使用9000端口"
    RAG_PORT=9000
    PIPELINE_PORT=9001

    # 更新配置文件
    sed -i 's/APP_PORT=9000/APP_PORT=9000/' backend/app/.env
    sed -i 's/DATA_PIPELINE_PORT=9001/DATA_PIPELINE_PORT=9001/' backend/app/data_pipeline.env
    sed -i 's/localhost:9000/localhost:9000/' backend/app/data_pipeline.env
else
    echo "使用默认端口9000/9001"
    RAG_PORT=9000
    PIPELINE_PORT=9001
fi

echo "最终端口配置："
echo "  RAG服务端口: $RAG_PORT"
echo "  数据管道端口: $PIPELINE_PORT"
```

### 6.3 配置Ubuntu防火墙 (UFW)

```bash
# 检查UFW状态
sudo ufw status

# 如果UFW未启用，先启用它
sudo ufw enable

# 开放必要端口
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 开放应用端口（根据实际配置）
if [ "$RAG_PORT" = "9000" ]; then
    sudo ufw allow 9000/tcp
    sudo ufw allow 9001/tcp
else
    sudo ufw allow 9000/tcp
    sudo ufw allow 9001/tcp
fi

# 查看防火墙规则
sudo ufw status numbered

# 如果需要删除规则
# sudo ufw delete [规则编号]
```

### 6.4 阿里云安全组配置

在阿里云控制台中配置安全组规则：

1. **登录阿里云控制台**
   - 进入ECS管理控制台
   - 选择对应的实例

2. **配置安全组规则**
   ```
   入方向规则：
   - 端口范围：22/22，协议：TCP，授权对象：0.0.0.0/0 (SSH)
   - 端口范围：80/80，协议：TCP，授权对象：0.0.0.0/0 (HTTP)
   - 端口范围：443/443，协议：TCP，授权对象：0.0.0.0/0 (HTTPS)
   - 端口范围：9000/9000，协议：TCP，授权对象：0.0.0.0/0 (RAG服务)
   - 端口范围：9001/9001，协议：TCP，授权对象：0.0.0.0/0 (数据管道)

   或者（如果使用9000/9001端口）：
   - 端口范围：9000/9000，协议：TCP，授权对象：0.0.0.0/0 (RAG服务)
   - 端口范围：9001/9001，协议：TCP，授权对象：0.0.0.0/0 (数据管道)
   ```

3. **安全建议**
   ```
   生产环境建议：
   - 将授权对象改为具体的IP地址或IP段
   - 例如：123.456.789.0/24 (限制特定网段访问)
   - 或者：123.456.789.123/32 (限制特定IP访问)
   ```

### 6.5 验证端口配置

```bash
# 验证防火墙配置
sudo ufw status

# 验证端口监听（服务启动后）
netstat -tlnp | grep -E ":($RAG_PORT|$PIPELINE_PORT|80|443)"

# 测试端口连通性（从外部）
# curl http://your_server_ip:$RAG_PORT/api/status
# curl http://your_server_ip:$PIPELINE_PORT/status
```

## 7. Nginx配置

### 7.1 检查Nginx安装和状态

```bash
# 检查Nginx是否已安装
nginx -v

# 检查Nginx状态
sudo systemctl status nginx

# 如果Nginx未运行，启动它
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查Nginx配置目录结构
ls -la /etc/nginx/
ls -la /etc/nginx/sites-available/
ls -la /etc/nginx/sites-enabled/
```

### 7.2 创建Nginx配置文件

```bash
# 创建RAG应用的Nginx配置
sudo vim /etc/nginx/sites-available/ragapp
```

### 7.3 Nginx配置内容

**根据实际端口配置选择对应的配置**：

```nginx
# RAG聊天应用 Nginx配置
# 适用于Ubuntu 22.04
server {
    listen 80;
    server_name your_domain.com your_server_ip;

    # 日志配置
    access_log /var/log/nginx/ragapp_access.log;
    error_log /var/log/nginx/ragapp_error.log;

    # 客户端上传大小限制
    client_max_body_size 100M;

    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 静态文件服务
    location /static/ {
        alias /opt/ragapp/fast-gzmdrw-chat/frontend/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";

        # 静态文件压缩
        gzip on;
        gzip_types text/css application/javascript text/javascript;
    }

    # 前端页面路由
    location / {
        # 直接代理到RAG服务的前端路由
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # RAG服务API代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS配置
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;

        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header Access-Control-Max-Age 1729000 always;
            add_header Content-Type 'text/plain; charset=utf-8' always;
            add_header Content-Length 0 always;
            return 204;
        }

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 数据管道API代理
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS配置
        add_header Access-Control-Allow-Origin * always;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;

        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header Access-Control-Max-Age 1729000 always;
            add_header Content-Type 'text/plain; charset=utf-8' always;
            add_header Content-Length 0 always;
            return 204;
        }

        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API文档路由
    location /docs {
        proxy_pass http://127.0.0.1:9000/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 数据管道API文档路由
    location /data-docs {
        rewrite ^/data-docs$ /docs break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 7.4 如果使用9000/9001端口的配置

如果您的服务运行在9000/9001端口，需要修改上述配置中的端口号：

```bash
# 创建端口替换脚本
cat > /tmp/update_nginx_ports.sh << 'EOF'
#!/bin/bash
# 将9000端口替换为9000，9001端口替换为9001
sed -i 's/127.0.0.1:9000/127.0.0.1:9000/g' /etc/nginx/sites-available/ragapp
sed -i 's/127.0.0.1:9001/127.0.0.1:9001/g' /etc/nginx/sites-available/ragapp
echo "端口配置已更新为9000/9001"
EOF

# 如果使用9000/9001端口，执行此脚本
chmod +x /tmp/update_nginx_ports.sh
sudo /tmp/update_nginx_ports.sh
```

### 7.5 启用Nginx配置

```bash
# 测试Nginx配置语法
sudo nginx -t

# 如果配置有误，检查错误信息并修正
if [ $? -ne 0 ]; then
    echo "Nginx配置有误，请检查配置文件"
    sudo nginx -t
    exit 1
fi

# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/

# 删除默认配置（可选，避免冲突）
sudo rm -f /etc/nginx/sites-enabled/default

# 再次测试配置
sudo nginx -t

# 重新加载Nginx配置
sudo systemctl reload nginx

# 确保Nginx开机自启
sudo systemctl enable nginx

# 检查Nginx状态
sudo systemctl status nginx
```

### 7.6 验证Nginx配置

```bash
# 检查Nginx进程
ps aux | grep nginx

# 检查端口监听
netstat -tlnp | grep nginx

# 检查配置文件
sudo nginx -T | grep -A 5 -B 5 "server_name"

# 测试HTTP访问
curl -I http://localhost/
curl -I http://your_server_ip/
```

## 8. 进程管理配置

### 8.1 选择进程管理方案

Ubuntu 22.04推荐使用**systemd**进行服务管理，它比supervisor更加现代化和稳定。

### 8.2 创建systemd服务文件

#### 8.2.1 创建RAG主服务

```bash
sudo vim /etc/systemd/system/ragapp-main.service
```

**RAG主服务配置**：
```ini
[Unit]
Description=RAG Chat Application Main Service
After=network.target
Wants=network.target

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/ragapp/fast-gzmdrw-chat/backend/app
Environment=PATH=/opt/ragapp/fast-gzmdrw-chat/venv/bin
EnvironmentFile=/opt/ragapp/fast-gzmdrw-chat/backend/app/.env
ExecStart=/opt/ragapp/fast-gzmdrw-chat/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ragapp-main

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/ragapp/fast-gzmdrw-chat/data /opt/ragapp/fast-gzmdrw-chat/storage /opt/ragapp/fast-gzmdrw-chat/logs
ProtectHome=true

[Install]
WantedBy=multi-user.target
```

#### 8.2.2 创建数据管道服务

```bash
sudo vim /etc/systemd/system/ragapp-pipeline.service
```

**数据管道服务配置**：
```ini
[Unit]
Description=RAG Chat Application Data Pipeline Service
After=network.target ragapp-main.service
Wants=network.target
Requires=ragapp-main.service

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/opt/ragapp/fast-gzmdrw-chat/backend/app
Environment=PATH=/opt/ragapp/fast-gzmdrw-chat/venv/bin
EnvironmentFile=/opt/ragapp/fast-gzmdrw-chat/backend/app/data_pipeline.env
ExecStart=/opt/ragapp/fast-gzmdrw-chat/venv/bin/python -m uvicorn data_pipeline:app --host 0.0.0.0 --port 9001
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=ragapp-pipeline

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/ragapp/fast-gzmdrw-chat/data /opt/ragapp/fast-gzmdrw-chat/storage /opt/ragapp/fast-gzmdrw-chat/logs
ProtectHome=true

[Install]
WantedBy=multi-user.target
```

### 8.3 如果使用9000/9001端口

如果您的服务使用9000/9001端口，需要修改服务文件中的端口：

```bash
# 修改RAG主服务端口
sudo sed -i 's/--port 9000/--port 9000/' /etc/systemd/system/ragapp-main.service

# 修改数据管道服务端口
sudo sed -i 's/--port 9001/--port 9001/' /etc/systemd/system/ragapp-pipeline.service
```

### 8.4 启动和管理systemd服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务（开机自启）
sudo systemctl enable ragapp-main.service
sudo systemctl enable ragapp-pipeline.service

# 启动服务
sudo systemctl start ragapp-main.service
sudo systemctl start ragapp-pipeline.service

# 查看服务状态
sudo systemctl status ragapp-main.service
sudo systemctl status ragapp-pipeline.service

# 查看服务日志
sudo journalctl -u ragapp-main.service -f
sudo journalctl -u ragapp-pipeline.service -f
```

### 8.5 常用服务管理命令

```bash
# 重启服务
sudo systemctl restart ragapp-main.service
sudo systemctl restart ragapp-pipeline.service

# 停止服务
sudo systemctl stop ragapp-main.service
sudo systemctl stop ragapp-pipeline.service

# 查看服务状态
sudo systemctl is-active ragapp-main.service
sudo systemctl is-enabled ragapp-main.service

# 查看所有RAG相关服务
sudo systemctl list-units | grep ragapp

# 查看服务启动失败原因
sudo systemctl status ragapp-main.service --no-pager -l
sudo journalctl -u ragapp-main.service --no-pager -l
```

### 8.6 验证服务运行

```bash
# 检查进程
ps aux | grep -E "(uvicorn|ragapp)"

# 检查端口监听
netstat -tlnp | grep -E ":(9000|9001|9000|9001)"

# 测试服务响应
curl http://localhost:9000/api/status
curl http://localhost:9001/status

# 查看系统资源使用
htop
```

### 8.7 备选方案：使用Supervisor（可选）

如果您更喜欢使用Supervisor，可以按以下步骤配置：

```bash
# 安装Supervisor
sudo apt install -y supervisor

# 创建配置文件
sudo vim /etc/supervisor/conf.d/ragapp.conf
```

**Supervisor配置内容**：
```ini
[program:ragapp-main]
command=/opt/ragapp/fast-gzmdrw-chat/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/fast-gzmdrw-chat/backend/app
user=ubuntu
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/fast-gzmdrw-chat/logs/ragapp-main.log
environment=PATH="/opt/ragapp/fast-gzmdrw-chat/venv/bin"

[program:ragapp-pipeline]
command=/opt/ragapp/fast-gzmdrw-chat/venv/bin/python -m uvicorn data_pipeline:app --host 0.0.0.0 --port 9001
directory=/opt/ragapp/fast-gzmdrw-chat/backend/app
user=ubuntu
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/fast-gzmdrw-chat/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/fast-gzmdrw-chat/venv/bin"
```

**Supervisor管理命令**：
```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start ragapp-main
sudo supervisorctl start ragapp-pipeline

# 查看状态
sudo supervisorctl status
```

## 9. SSL证书配置（推荐）

### 9.1 安装Certbot

```bash
# 更新包列表
sudo apt update

# 安装Certbot和Nginx插件
sudo apt install -y certbot python3-certbot-nginx

# 验证安装
certbot --version
```

### 9.2 获取SSL证书

**前提条件**：
- 域名已正确解析到服务器IP
- Nginx已正确配置并运行
- 防火墙已开放80和443端口

```bash
# 获取SSL证书（自动配置Nginx）
sudo certbot --nginx -d your_domain.com

# 如果有多个域名
sudo certbot --nginx -d your_domain.com -d www.your_domain.com

# 仅获取证书，不自动配置Nginx
sudo certbot certonly --nginx -d your_domain.com
```

### 9.3 验证SSL配置

```bash
# 检查证书状态
sudo certbot certificates

# 测试SSL配置
curl -I https://your_domain.com

# 检查证书有效期
openssl x509 -in /etc/letsencrypt/live/your_domain.com/fullchain.pem -text -noout | grep "Not After"
```

### 9.4 自动续期配置

```bash
# 测试自动续期
sudo certbot renew --dry-run

# 查看自动续期定时任务
sudo systemctl list-timers | grep certbot

# 如果没有自动配置，手动添加
sudo crontab -e

# 添加以下行（每天检查一次）
0 12 * * * /usr/bin/certbot renew --quiet && /usr/bin/systemctl reload nginx
```

### 9.5 SSL安全配置优化

编辑Nginx配置以增强SSL安全性：

```bash
sudo vim /etc/nginx/sites-available/ragapp
```

在server块中添加SSL安全配置：

```nginx
# SSL安全配置
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# HSTS (HTTP Strict Transport Security)
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

# 其他安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
```

## 10. 启动和测试

### 10.1 启动所有服务

```bash
# 启动并检查systemd服务
sudo systemctl start ragapp-main.service
sudo systemctl start ragapp-pipeline.service
sudo systemctl start nginx

# 检查服务状态
sudo systemctl status ragapp-main.service --no-pager
sudo systemctl status ragapp-pipeline.service --no-pager
sudo systemctl status nginx --no-pager

# 检查服务是否开机自启
sudo systemctl is-enabled ragapp-main.service
sudo systemctl is-enabled ragapp-pipeline.service
sudo systemctl is-enabled nginx
```

### 10.2 验证端口监听

```bash
# 检查应用端口
netstat -tlnp | grep -E ":(9000|9001|9000|9001)"

# 检查Web端口
netstat -tlnp | grep -E ":(80|443)"

# 使用ss命令（更现代）
ss -tlnp | grep -E ":(9000|9001|9000|9001|80|443)"
```

### 10.3 测试API接口

```bash
# 测试RAG主服务
echo "测试RAG主服务..."
curl -s http://localhost:9000/api/status | jq . || curl -s http://localhost:9000/api/status

# 测试数据管道服务
echo "测试数据管道服务..."
curl -s http://localhost:9001/status | jq . || curl -s http://localhost:9001/status

# 通过Nginx代理测试
echo "测试Nginx代理..."
curl -s http://localhost/api/status | jq . || curl -s http://localhost/api/status
curl -s http://localhost/data-api/status | jq . || curl -s http://localhost/data-api/status

# 如果配置了域名，测试域名访问
if [ -n "$YOUR_DOMAIN" ]; then
    echo "测试域名访问..."
    curl -s http://$YOUR_DOMAIN/api/status
    curl -s https://$YOUR_DOMAIN/api/status  # 如果配置了SSL
fi
```

### 10.4 测试前端页面

```bash
# 测试前端页面响应
curl -I http://localhost/
curl -I http://your_server_ip/

# 如果配置了SSL
curl -I https://your_domain.com/
```

### 10.5 完整功能测试

```bash
# 创建功能测试脚本
cat > /opt/ragapp/test_deployment.sh << 'EOF'
#!/bin/bash

echo "🚀 RAG应用部署测试"
echo "===================="

# 测试服务状态
echo "1. 检查服务状态..."
systemctl is-active ragapp-main.service && echo "✅ RAG主服务运行正常" || echo "❌ RAG主服务异常"
systemctl is-active ragapp-pipeline.service && echo "✅ 数据管道服务运行正常" || echo "❌ 数据管道服务异常"
systemctl is-active nginx && echo "✅ Nginx运行正常" || echo "❌ Nginx异常"

# 测试端口监听
echo -e "\n2. 检查端口监听..."
netstat -tlnp | grep ":9000 " > /dev/null && echo "✅ 端口9000监听正常" || echo "❌ 端口9000未监听"
netstat -tlnp | grep ":9001 " > /dev/null && echo "✅ 端口9001监听正常" || echo "❌ 端口9001未监听"
netstat -tlnp | grep ":80 " > /dev/null && echo "✅ 端口80监听正常" || echo "❌ 端口80未监听"

# 测试API响应
echo -e "\n3. 测试API响应..."
curl -s http://localhost:9000/api/status > /dev/null && echo "✅ RAG API响应正常" || echo "❌ RAG API无响应"
curl -s http://localhost:9001/status > /dev/null && echo "✅ 数据管道API响应正常" || echo "❌ 数据管道API无响应"
curl -s http://localhost/ > /dev/null && echo "✅ Nginx代理响应正常" || echo "❌ Nginx代理无响应"

# 测试数据库连接
echo -e "\n4. 测试数据库..."
ls -la /opt/ragapp/storage/chroma.sqlite3 > /dev/null 2>&1 && echo "✅ SQLite数据库文件存在" || echo "⚠️ SQLite数据库文件不存在（首次运行正常）"

echo -e "\n🎉 测试完成！"
EOF

chmod +x /opt/ragapp/test_deployment.sh
/opt/ragapp/test_deployment.sh
```

### 10.6 访问应用

部署成功后，您可以通过以下方式访问应用：

- **前端聊天界面**:
  - HTTP: `http://your_domain.com` 或 `http://your_server_ip`
  - HTTPS: `https://your_domain.com`（如果配置了SSL）

- **文档管理界面**:
  - HTTP: `http://your_domain.com/documents`
  - HTTPS: `https://your_domain.com/documents`

- **API文档**:
  - RAG服务API: `http://your_domain.com/docs`
  - 数据管道API: `http://your_domain.com/data-docs`

- **直接API访问**:
  - RAG API: `http://your_domain.com/api/`
  - 数据管道API: `http://your_domain.com/data-api/`

## 11. 故障排除

### 11.1 常见问题及解决方案

#### 11.1.1 服务启动问题

**问题1: 端口被占用**
```bash
# 查找占用端口的进程
sudo lsof -i :9000
sudo lsof -i :9001

# 或使用ss命令
ss -tlnp | grep -E ":(9000|9001)"

# 杀死占用进程
sudo kill -9 PID

# 或者修改配置使用其他端口
vim backend/app/.env  # 修改APP_PORT
vim backend/app/data_pipeline.env  # 修改DATA_PIPELINE_PORT
```

**问题2: 权限问题**
```bash
# 修复文件权限
sudo chown -R ubuntu:ubuntu /opt/ragapp
sudo chmod -R 755 /opt/ragapp

# 修复配置文件权限
chmod 600 /opt/ragapp/backend/app/.env
chmod 600 /opt/ragapp/backend/app/data_pipeline.env

# 修复数据目录权限
chmod 755 /opt/ragapp/data /opt/ragapp/storage /opt/ragapp/logs
```

**问题3: Python依赖问题**
```bash
# 重新安装依赖
cd /opt/ragapp
source venv/bin/activate
pip install --upgrade pip
pip install --upgrade -r requirements.txt

# 如果某些包安装失败，单独安装
pip install fastapi uvicorn llama-index chromadb
```

**问题4: 虚拟环境问题**
```bash
# 重新创建虚拟环境
cd /opt/ragapp
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

#### 11.1.2 数据库连接问题

**SQLite3问题**
```bash
# 检查SQLite文件权限
ls -la /opt/ragapp/storage/
chmod 644 /opt/ragapp/storage/chroma.sqlite3

# 测试SQLite连接
sqlite3 /opt/ragapp/storage/chroma.sqlite3 ".tables"
```

**MySQL连接问题**
```bash
# 测试MySQL连接
mysql -h your_cms_host -P 3306 -u your_username -p

# 检查网络连通性
ping your_cms_host
telnet your_cms_host 3306

# 检查防火墙
sudo ufw status
```

#### 11.1.3 Nginx配置问题

**配置语法错误**
```bash
# 测试Nginx配置
sudo nginx -t

# 查看详细错误信息
sudo nginx -T

# 重新加载配置
sudo systemctl reload nginx
```

**代理问题**
```bash
# 检查上游服务是否运行
curl http://localhost:9000/api/status
curl http://localhost:9001/status

# 检查Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 11.2 日志查看和分析

#### 11.2.1 systemd服务日志
```bash
# 查看RAG主服务日志
sudo journalctl -u ragapp-main.service -f
sudo journalctl -u ragapp-main.service --since "1 hour ago"

# 查看数据管道服务日志
sudo journalctl -u ragapp-pipeline.service -f
sudo journalctl -u ragapp-pipeline.service --since "1 hour ago"

# 查看所有RAG相关服务日志
sudo journalctl -u ragapp-* -f
```

#### 11.2.2 Nginx日志
```bash
# 查看访问日志
sudo tail -f /var/log/nginx/ragapp_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/ragapp_error.log

# 分析访问日志
sudo grep "ERROR\|error" /var/log/nginx/ragapp_error.log
sudo grep "POST\|GET" /var/log/nginx/ragapp_access.log | tail -20
```

#### 11.2.3 应用日志
```bash
# 如果使用文件日志
tail -f /opt/ragapp/logs/*.log

# 查看Python错误
sudo journalctl -u ragapp-main.service | grep -i "error\|exception\|traceback"
```

### 11.3 服务管理命令

#### 11.3.1 systemd服务管理
```bash
# 重启服务
sudo systemctl restart ragapp-main.service
sudo systemctl restart ragapp-pipeline.service

# 停止服务
sudo systemctl stop ragapp-main.service ragapp-pipeline.service

# 启动服务
sudo systemctl start ragapp-main.service ragapp-pipeline.service

# 查看服务状态
sudo systemctl status ragapp-main.service ragapp-pipeline.service

# 重新加载服务配置
sudo systemctl daemon-reload
```

#### 11.3.2 Nginx管理
```bash
# 重启Nginx
sudo systemctl restart nginx

# 重新加载配置（不中断服务）
sudo systemctl reload nginx

# 测试配置
sudo nginx -t

# 查看Nginx状态
sudo systemctl status nginx
```

### 11.4 性能问题排查

#### 11.4.1 系统资源监控
```bash
# 查看系统资源使用
htop
free -h
df -h

# 查看进程资源使用
ps aux | grep -E "(uvicorn|python)" | head -10

# 查看网络连接
netstat -an | grep -E ":(9000|9001)" | wc -l
```

#### 11.4.2 应用性能测试
```bash
# 测试API响应时间
time curl http://localhost:9000/api/status

# 压力测试（需要安装ab）
sudo apt install apache2-utils
ab -n 100 -c 10 http://localhost:9000/api/status
```

### 11.5 数据备份和恢复

#### 11.5.1 备份重要数据
```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 备份SQLite数据库
cp ./storage/chroma.sqlite3 ./backups/chroma_$(date +%Y%m%d_%H%M%S).sqlite3

# 备份配置文件
tar -czf ./backups/config_$(date +%Y%m%d_%H%M%S).tar.gz \
    ./backend/app/.env \
    ./backend/app/data_pipeline.env \
    /etc/nginx/sites-available/ragapp \
    /etc/systemd/system/ragapp-*.service

# 备份数据目录
tar -czf ./backups/data_$(date +%Y%m%d_%H%M%S).tar.gz ./data/
```

#### 11.5.2 恢复数据
```bash
# 进入项目目录
cd /opt/ragapp/fast-gzmdrw-chat

# 恢复SQLite数据库
cp ./backups/chroma_YYYYMMDD_HHMMSS.sqlite3 ./storage/chroma.sqlite3

# 恢复配置文件
tar -xzf ./backups/config_YYYYMMDD_HHMMSS.tar.gz -C /

# 重启服务
sudo systemctl restart ragapp-main.service ragapp-pipeline.service nginx
```

## 12. 维护和监控

### 12.1 定期维护任务

#### 12.1.1 创建维护脚本
```bash
# 创建维护脚本目录
mkdir -p /opt/ragapp/scripts

# 创建日常维护脚本
cat > /opt/ragapp/scripts/daily_maintenance.sh << 'EOF'
#!/bin/bash
# RAG应用日常维护脚本

LOG_FILE="/opt/ragapp/fast-gzmdrw-chat/logs/maintenance.log"
BACKUP_DIR="/opt/ragapp/fast-gzmdrw-chat/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "$(date): 开始日常维护任务" >> $LOG_FILE

# 1. 清理旧日志文件（保留30天）
find /var/log/nginx/ -name "ragapp_*.log.*" -mtime +30 -delete
find /opt/ragapp/fast-gzmdrw-chat/logs/ -name "*.log.*" -mtime +30 -delete

# 2. 备份SQLite数据库
if [ -f "/opt/ragapp/fast-gzmdrw-chat/storage/chroma.sqlite3" ]; then
    cp /opt/ragapp/fast-gzmdrw-chat/storage/chroma.sqlite3 $BACKUP_DIR/chroma_$DATE.sqlite3
    echo "$(date): SQLite数据库备份完成" >> $LOG_FILE
fi

# 3. 清理旧备份（保留7天）
find $BACKUP_DIR -name "chroma_*.sqlite3" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

# 4. 检查磁盘空间
DISK_USAGE=$(df /opt/ragapp/fast-gzmdrw-chat | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): 警告 - 磁盘使用率超过80%: $DISK_USAGE%" >> $LOG_FILE
fi

# 5. 检查服务状态
systemctl is-active ragapp-main.service > /dev/null || echo "$(date): 警告 - RAG主服务未运行" >> $LOG_FILE
systemctl is-active ragapp-pipeline.service > /dev/null || echo "$(date): 警告 - 数据管道服务未运行" >> $LOG_FILE
systemctl is-active nginx > /dev/null || echo "$(date): 警告 - Nginx未运行" >> $LOG_FILE

echo "$(date): 日常维护任务完成" >> $LOG_FILE
EOF

chmod +x /opt/ragapp/scripts/daily_maintenance.sh
```

#### 12.1.2 设置定时任务
```bash
# 添加到crontab
crontab -e

# 添加以下行
# 每天凌晨2点执行维护任务
0 2 * * * /opt/ragapp/scripts/daily_maintenance.sh

# 每小时检查服务状态
0 * * * * systemctl is-active ragapp-main.service ragapp-pipeline.service nginx > /dev/null || /opt/ragapp/scripts/restart_services.sh
```

### 12.2 监控脚本

#### 12.2.1 创建健康检查脚本
```bash
cat > /opt/ragapp/scripts/health_check.sh << 'EOF'
#!/bin/bash
# RAG应用健康检查脚本

echo "🏥 RAG应用健康检查报告"
echo "========================"
echo "检查时间: $(date)"
echo

# 1. 系统资源检查
echo "📊 系统资源状态:"
echo "内存使用: $(free -h | grep '^Mem:' | awk '{print $3"/"$2}')"
echo "磁盘使用: $(df -h /opt/ragapp/fast-gzmdrw-chat | tail -1 | awk '{print $3"/"$2" ("$5")"}')"
echo "CPU负载: $(uptime | awk -F'load average:' '{print $2}')"
echo

# 2. 服务状态检查
echo "🔧 服务状态:"
for service in ragapp-main ragapp-pipeline nginx; do
    if systemctl is-active $service > /dev/null; then
        echo "✅ $service: 运行正常"
    else
        echo "❌ $service: 服务异常"
    fi
done
echo

# 3. 端口监听检查
echo "🌐 端口监听状态:"
for port in 9000 9001 80 443; do
    if netstat -tlnp | grep ":$port " > /dev/null; then
        echo "✅ 端口 $port: 监听正常"
    else
        echo "❌ 端口 $port: 未监听"
    fi
done
echo

# 4. API响应检查
echo "🔌 API响应测试:"
if curl -s http://localhost:9000/api/status > /dev/null; then
    echo "✅ RAG API: 响应正常"
else
    echo "❌ RAG API: 无响应"
fi

if curl -s http://localhost:9001/status > /dev/null; then
    echo "✅ 数据管道API: 响应正常"
else
    echo "❌ 数据管道API: 无响应"
fi
echo

# 5. 数据库状态检查
echo "💾 数据库状态:"
if [ -f "/opt/ragapp/fast-gzmdrw-chat/storage/chroma.sqlite3" ]; then
    SIZE=$(du -h /opt/ragapp/fast-gzmdrw-chat/storage/chroma.sqlite3 | cut -f1)
    echo "✅ SQLite数据库: 存在 (大小: $SIZE)"
else
    echo "⚠️ SQLite数据库: 不存在"
fi
echo

# 6. 日志错误检查
echo "📝 最近错误日志:"
ERROR_COUNT=$(journalctl -u ragapp-main.service -u ragapp-pipeline.service --since "1 hour ago" | grep -i "error\|exception" | wc -l)
if [ $ERROR_COUNT -eq 0 ]; then
    echo "✅ 最近1小时无错误日志"
else
    echo "⚠️ 最近1小时发现 $ERROR_COUNT 条错误日志"
fi

echo
echo "🎯 健康检查完成!"
EOF

chmod +x /opt/ragapp/scripts/health_check.sh
```

#### 12.2.2 创建服务重启脚本
```bash
cat > /opt/ragapp/scripts/restart_services.sh << 'EOF'
#!/bin/bash
# 服务重启脚本

LOG_FILE="/opt/ragapp/logs/restart.log"

echo "$(date): 检测到服务异常，开始重启服务" >> $LOG_FILE

# 重启应用服务
sudo systemctl restart ragapp-main.service
sudo systemctl restart ragapp-pipeline.service

# 等待服务启动
sleep 10

# 检查服务状态
if systemctl is-active ragapp-main.service > /dev/null && systemctl is-active ragapp-pipeline.service > /dev/null; then
    echo "$(date): 服务重启成功" >> $LOG_FILE
else
    echo "$(date): 服务重启失败，需要人工干预" >> $LOG_FILE
fi
EOF

chmod +x /opt/ragapp/scripts/restart_services.sh
```

### 12.3 性能监控

#### 12.3.1 创建性能监控脚本
```bash
cat > /opt/ragapp/scripts/performance_monitor.sh << 'EOF'
#!/bin/bash
# 性能监控脚本

MONITOR_LOG="/opt/ragapp/logs/performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 获取系统指标
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
MEM_USAGE=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')
DISK_USAGE=$(df /opt/ragapp | tail -1 | awk '{print $5}' | sed 's/%//')

# 获取应用进程资源使用
RAG_CPU=$(ps aux | grep "uvicorn main:app" | grep -v grep | awk '{print $3}')
RAG_MEM=$(ps aux | grep "uvicorn main:app" | grep -v grep | awk '{print $4}')
PIPELINE_CPU=$(ps aux | grep "uvicorn data_pipeline:app" | grep -v grep | awk '{print $3}')
PIPELINE_MEM=$(ps aux | grep "uvicorn data_pipeline:app" | grep -v grep | awk '{print $4}')

# 记录到日志
echo "$DATE,CPU:$CPU_USAGE,MEM:$MEM_USAGE,DISK:$DISK_USAGE,RAG_CPU:$RAG_CPU,RAG_MEM:$RAG_MEM,PIPELINE_CPU:$PIPELINE_CPU,PIPELINE_MEM:$PIPELINE_MEM" >> $MONITOR_LOG

# 如果资源使用过高，发出警告
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    echo "$(date): 警告 - CPU使用率过高: $CPU_USAGE%" >> /opt/ragapp/logs/alerts.log
fi

if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
    echo "$(date): 警告 - 内存使用率过高: $MEM_USAGE%" >> /opt/ragapp/logs/alerts.log
fi

if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): 警告 - 磁盘使用率过高: $DISK_USAGE%" >> /opt/ragapp/logs/alerts.log
fi
EOF

chmod +x /opt/ragapp/scripts/performance_monitor.sh

# 添加到crontab，每5分钟执行一次
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/ragapp/scripts/performance_monitor.sh") | crontab -
```

### 12.4 日志轮转配置

```bash
# 创建logrotate配置
sudo vim /etc/logrotate.d/ragapp

# 添加以下内容
/opt/ragapp/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        systemctl reload ragapp-main.service ragapp-pipeline.service > /dev/null 2>&1 || true
    endscript
}

/var/log/nginx/ragapp_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
```

### 12.5 使用监控脚本

```bash
# 运行健康检查
/opt/ragapp/scripts/health_check.sh

# 查看性能日志
tail -f /opt/ragapp/logs/performance.log

# 查看告警日志
tail -f /opt/ragapp/logs/alerts.log

# 查看维护日志
tail -f /opt/ragapp/logs/maintenance.log
```

---

## 📞 技术支持

### 常用管理命令速查

```bash
# 服务管理
sudo systemctl status ragapp-main.service ragapp-pipeline.service nginx
sudo systemctl restart ragapp-main.service ragapp-pipeline.service
sudo journalctl -u ragapp-main.service -f

# 健康检查
/opt/ragapp/scripts/health_check.sh
curl http://localhost:9000/api/status
curl http://localhost:9001/status

# 日志查看
sudo tail -f /var/log/nginx/ragapp_error.log
sudo journalctl -u ragapp-main.service --since "1 hour ago"

# 备份恢复
/opt/ragapp/scripts/daily_maintenance.sh
cp /opt/ragapp/backups/chroma_*.sqlite3 /opt/ragapp/storage/chroma.sqlite3
```

### 部署完成检查清单

- [ ] ✅ Ubuntu 22.04系统更新完成
- [ ] ✅ Python虚拟环境创建并激活
- [ ] ✅ 项目依赖安装完成
- [ ] ✅ SQLite3数据库配置正确
- [ ] ✅ MySQL远程连接配置正确
- [ ] ✅ 环境变量配置完成
- [ ] ✅ 防火墙端口开放
- [ ] ✅ Nginx配置并启动
- [ ] ✅ systemd服务配置并启动
- [ ] ✅ SSL证书配置（可选）
- [ ] ✅ 监控脚本部署
- [ ] ✅ 定时任务配置
- [ ] ✅ 前端页面可访问
- [ ] ✅ API接口响应正常

### 联系支持

如果在部署过程中遇到问题，请：

1. **查看日志**: 使用健康检查脚本和日志查看命令
2. **检查配置**: 验证环境变量和服务配置
3. **测试连接**: 确认数据库和网络连接正常
4. **资源检查**: 确认系统资源充足

部署完成后，您的RAG聊天应用将提供：
- **智能问答服务**: 基于自定义提示词的高质量回答
- **文档管理功能**: 可视化的文档上传和管理
- **数据同步服务**: CMS与RAG系统的自动同步
- **API接口**: 完整的RESTful API支持
- **监控告警**: 自动化的健康检查和性能监控

🎉 **恭喜！您的RAG聊天应用已成功部署到Ubuntu 22.04服务器！**

### 11.4 性能优化建议

**1. 系统优化**
```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

**2. Nginx优化**
```bash
# 编辑Nginx主配置
sudo vim /etc/nginx/nginx.conf

# 添加以下配置
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

**3. 数据库优化**
```bash
# 编辑MySQL配置
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加以下配置
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
```

---

## 📞 技术支持

### 常用管理命令
```bash
# 查看所有服务状态
sudo supervisorctl status
sudo systemctl status nginx mysql

# 重启所有服务
sudo supervisorctl restart all
sudo systemctl restart nginx

# 查看实时日志
tail -f /opt/ragapp/logs/*.log

# 备份数据
mysqldump -u ragapp -p ragapp > backup.sql
tar -czf data_backup.tar.gz /opt/ragapp/data/
```

### 监控脚本
创建简单的监控脚本：
```bash
#!/bin/bash
# 保存为 /opt/ragapp/monitor.sh

echo "=== RAG应用状态监控 ==="
echo "时间: $(date)"
echo

echo "=== 服务状态 ==="
sudo supervisorctl status

echo "=== 端口监听 ==="
netstat -tlnp | grep -E ':(9000|9001|80|443)'

echo "=== 系统资源 ==="
free -h
df -h /opt/ragapp

echo "=== 最近错误 ==="
tail -n 5 /opt/ragapp/logs/ragapp-main.log | grep -i error || echo "无错误"
```

如果在部署过程中遇到问题，请检查：
1. 服务器系统日志
2. 应用程序日志
3. Nginx错误日志
4. 防火墙和安全组配置

部署完成后，您的RAG聊天应用将通过以下方式访问：
- **前端界面**: `http://your_domain.com`
- **RAG API**: `http://your_domain.com/api/`
- **数据管道API**: `http://your_domain.com/data-api/`
- **API文档**: `http://your_domain.com/api/docs`

## 2. 项目文件上传

### 2.1 创建项目目录
```bash
# 创建项目根目录
sudo mkdir -p /opt/ragapp
sudo chown $USER:$USER /opt/ragapp
cd /opt/ragapp
```

### 2.2 上传项目文件
有几种方式上传项目文件：

**方式1: 使用SCP上传**
```bash
# 在本地执行，上传整个项目
scp -r /path/to/your/fast-gzmdrw-chat root@your_server_ip:/opt/ragapp/

# 或者打包后上传
tar -czf ragapp.tar.gz fast-gzmdrw-chat/
scp ragapp.tar.gz root@your_server_ip:/opt/ragapp/
```

**方式2: 使用Git克隆**
```bash
# 如果项目在Git仓库中
cd /opt/ragapp
git clone https://github.com/your-username/your-repo.git .
```

**方式3: 使用SFTP工具**
- 使用FileZilla、WinSCP等工具上传

### 2.3 设置文件权限
```bash
cd /opt/ragapp
sudo chown -R $USER:$USER .
chmod -R 755 .
```

## 3. Python环境配置

### 3.1 创建虚拟环境
```bash
cd /opt/ragapp
python3 -m venv venv
source venv/bin/activate
```

### 3.2 升级pip
```bash
pip install --upgrade pip
```

### 3.3 安装项目依赖
```bash
pip install -r requirements.txt
```

### 3.4 验证安装
```bash
python -c "import fastapi, uvicorn, llama_index; print('Dependencies installed successfully')"
```

## 4. 数据库配置

### 4.1 启动MySQL服务
```bash
# CentOS/RHEL
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 4.2 安全配置MySQL
```bash
sudo mysql_secure_installation
```

### 4.3 创建数据库和用户
```bash
# 登录MySQL
sudo mysql -u root -p

# 在MySQL中执行
CREATE DATABASE ragapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ragapp'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON ragapp.* TO 'ragapp'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 5. 环境变量配置

### 5.1 创建主环境配置文件
```bash
cd /opt/ragapp
cp backend/app/data_pipeline.env .env
```

### 5.2 编辑环境变量
```bash
vim .env
```

添加以下配置：
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai-proxy.org/v1

# 数据库配置
DATABASE_URL=mysql+pymysql://ragapp:your_strong_password@localhost:3306/ragapp

# 服务端口配置（避免冲突）
RAG_SERVICE_PORT=9000
DATA_PIPELINE_PORT=9001

# 文件存储路径
DATA_DIR=/opt/ragapp/data
STORAGE_DIR=/opt/ragapp/storage

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ragapp/logs/app.log

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=your_domain.com,your_server_ip
```

### 5.3 创建日志目录
```bash
mkdir -p /opt/ragapp/logs
mkdir -p /opt/ragapp/data
mkdir -p /opt/ragapp/storage
```

## 6. 端口配置

### 6.1 检查端口占用
```bash
# 检查常用端口是否被占用
netstat -tlnp | grep -E ':(9000|9001|9000|9001)'
```

### 6.2 修改应用端口配置
编辑后端配置文件：

**修改 backend/config/settings.py**
```python
# 如果9000端口被占用，改为9000
RAG_SERVICE_PORT = int(os.getenv("RAG_SERVICE_PORT", "9000"))
DATA_PIPELINE_PORT = int(os.getenv("DATA_PIPELINE_PORT", "9001"))
```

### 6.3 开放防火墙端口
```bash
# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --permanent --add-port=9001/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian (ufw)
sudo ufw allow 9000/tcp
sudo ufw allow 9001/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 6.4 阿里云安全组配置
在阿里云控制台中：
1. 进入ECS实例管理
2. 点击"安全组"
3. 添加安全组规则：
   - 端口范围：9000/9000，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：9001/9001，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：80/80，协议：TCP，授权对象：0.0.0.0/0
   - 端口范围：443/443，协议：TCP，授权对象：0.0.0.0/0

## 7. Nginx配置

### 7.1 创建Nginx配置文件
```bash
sudo vim /etc/nginx/sites-available/ragapp
```

### 7.2 Nginx配置内容
```nginx
# RAG聊天应用 Nginx配置
server {
    listen 80;
    server_name your_domain.com your_server_ip;
    
    # 日志配置
    access_log /var/log/nginx/ragapp_access.log;
    error_log /var/log/nginx/ragapp_error.log;
    
    # 客户端上传大小限制
    client_max_body_size 100M;
    
    # 静态文件服务
    location /static/ {
        alias /opt/ragapp/frontend/static/;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 前端页面
    location / {
        root /opt/ragapp/frontend/templates;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # RAG服务API代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1729000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 数据管道API代理
    location /data-api/ {
        rewrite ^/data-api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 跨域配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1729000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 7.3 启用配置
```bash
# 创建软链接启用站点
sudo ln -s /etc/nginx/sites-available/ragapp /etc/nginx/sites-enabled/

# 删除默认配置（可选）
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 8. 进程管理配置

### 8.1 安装Supervisor
```bash
# CentOS/RHEL
sudo yum install -y supervisor

# Ubuntu/Debian
sudo apt install -y supervisor
```

### 8.2 创建RAG服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-main.conf
```

配置内容：
```ini
[program:ragapp-main]
command=/opt/ragapp/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 9000
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-main.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.3 创建数据管道服务配置
```bash
sudo vim /etc/supervisor/conf.d/ragapp-pipeline.conf
```

配置内容：
```ini
[program:ragapp-pipeline]
command=/opt/ragapp/venv/bin/python data_pipeline.py
directory=/opt/ragapp/backend/app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/opt/ragapp/logs/ragapp-pipeline.log
environment=PATH="/opt/ragapp/venv/bin"
```

### 8.4 启动Supervisor服务
```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start ragapp-main
sudo supervisorctl start ragapp-pipeline

# 查看状态
sudo supervisorctl status

# 设置开机自启
sudo systemctl enable supervisor
```

## 9. SSL证书配置（可选但推荐）

### 9.1 安装Certbot
```bash
# CentOS/RHEL
sudo yum install -y certbot python3-certbot-nginx

# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx
```

### 9.2 获取SSL证书
```bash
sudo certbot --nginx -d your_domain.com
```

### 9.3 自动续期
```bash
# 添加到crontab
sudo crontab -e

# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 10. 启动和测试

### 10.1 检查服务状态
```bash
# 检查Supervisor管理的进程
sudo supervisorctl status

# 检查端口监听
netstat -tlnp | grep -E ':(9000|9001)'

# 检查Nginx状态
sudo systemctl status nginx
```

### 10.2 测试API接口
```bash
# 测试RAG服务
curl http://your_server_ip/api/status

# 测试数据管道服务
curl http://your_server_ip/data-api/articles/summary
```

### 10.3 访问前端页面
在浏览器中访问：
- HTTP: `http://your_domain.com` 或 `http://your_server_ip`
- HTTPS: `https://your_domain.com`（如果配置了SSL）

## 11. 故障排除

### 11.1 常见问题

**问题1: 端口被占用**
```bash
# 查找占用端口的进程
sudo lsof -i :9000
sudo lsof -i :9001

# 杀死进程
sudo kill -9 PID
```

**问题2: 权限问题**
```bash
# 修复文件权限
sudo chown -R www-data:www-data /opt/ragapp
sudo chmod -R 755 /opt/ragapp
```

**问题3: Python依赖问题**
```bash
# 重新安装依赖
cd /opt/ragapp
source venv/bin/activate
pip install --upgrade -r requirements.txt
```

### 11.2 日志查看
```bash
# 查看应用日志
tail -f /opt/ragapp/logs/ragapp-main.log
tail -f /opt/ragapp/logs/ragapp-pipeline.log

# 查看Nginx日志
tail -f /var/log/nginx/ragapp_access.log
tail -f /var/log/nginx/ragapp_error.log

# 查看系统日志
journalctl -u nginx -f
journalctl -u supervisor -f
```

### 11.3 重启服务
```bash
# 重启应用服务
sudo supervisorctl restart ragapp-main
sudo supervisorctl restart ragapp-pipeline

# 重启Nginx
sudo systemctl restart nginx

# 重启所有相关服务
sudo systemctl restart supervisor nginx
```

## 12. 维护和监控

### 12.1 定期备份
```bash
# 创建备份脚本
sudo vim /opt/ragapp/scripts/backup.sh
```

### 12.2 监控脚本
```bash
# 创建监控脚本
sudo vim /opt/ragapp/scripts/monitor.sh
```

### 12.3 日志轮转
```bash
# 配置logrotate
sudo vim /etc/logrotate.d/ragapp
```

---

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 服务器系统日志
2. 应用程序日志
3. Nginx错误日志
4. 防火墙和安全组配置

部署完成后，您的RAG聊天应用将通过以下方式访问：
- 前端界面: `http://your_domain.com`
- RAG API: `http://your_domain.com/api/`
- 数据管道API: `http://your_domain.com/data-api/`
