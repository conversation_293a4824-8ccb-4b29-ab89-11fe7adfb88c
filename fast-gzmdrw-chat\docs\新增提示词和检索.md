# RAG提示词工程配置指南

## 📍 配置位置

提示词工程的主要配置位置在 `backend/app/rag_service.py` 文件中的 `_setup_custom_prompts()` 方法。

## 🔧 当前配置

### 1. 自定义QA提示词模板

位置：`rag_service.py` 第54-80行

```python
def _setup_custom_prompts(self):
    qa_template = """
你是贵阳人文科技学院的智能助手"文文"，专门为师生提供准确、有用的信息服务。

请根据以下上下文信息回答用户的问题。回答时请遵循以下原则：

1. **准确性**：严格基于提供的上下文信息回答，不要编造或推测信息
2. **完整性**：尽可能提供完整、详细的答案
3. **友好性**：使用亲切、专业的语调
4. **结构化**：对于复杂信息，使用清晰的结构组织答案
5. **学校特色**：突出贵阳人文科技学院的特色和优势

如果上下文信息不足以回答问题，请诚实说明，并建议用户联系相关部门获取更详细信息。

上下文信息：
{context_str}

用户问题：{query_str}

请提供准确、有用的回答：
"""
```

### 2. 配置参数

在 `backend/config/settings.py` 中可以调整以下参数：

```python
# RAG配置
similarity_top_k: int = 5          # 检索的文档数量
response_mode: str = "compact"      # 响应模式
temperature: float = 0.1            # LLM温度参数
max_tokens: int = 1000             # 最大token数
```

## 🎯 优化建议

### 1. 针对不同问题类型的提示词

可以根据问题类型创建不同的提示词模板：

```python
# 学术问题提示词
academic_template = """
你是贵阳人文科技学院的学术顾问...
"""

# 生活服务问题提示词
service_template = """
你是贵阳人文科技学院的生活服务助手...
"""

# 招生咨询问题提示词
admission_template = """
你是贵阳人文科技学院的招生咨询顾问...
"""
```

### 2. 响应模式选择

- `compact`: 紧凑模式，适合大多数情况
- `tree_summarize`: 树形总结，适合长文档
- `simple_summarize`: 简单总结，适合快速回答

### 3. 温度参数调整

- `0.0-0.3`: 更准确、一致的回答
- `0.4-0.7`: 平衡准确性和创造性
- `0.8-1.0`: 更有创造性但可能不够准确

## 🔄 动态提示词配置

### 方法1：基于问题分类

```python
def get_prompt_by_category(self, question: str) -> PromptTemplate:
    """根据问题类别选择合适的提示词"""
    if "专业" in question or "课程" in question:
        return self.academic_prompt
    elif "宿舍" in question or "食堂" in question:
        return self.service_prompt
    elif "招生" in question or "录取" in question:
        return self.admission_prompt
    else:
        return self.custom_qa_prompt
```

### 方法2：基于用户角色

```python
def get_prompt_by_role(self, user_role: str) -> PromptTemplate:
    """根据用户角色选择合适的提示词"""
    role_prompts = {
        "student": self.student_prompt,
        "teacher": self.teacher_prompt,
        "visitor": self.visitor_prompt
    }
    return role_prompts.get(user_role, self.custom_qa_prompt)
```

## 📊 效果评估

### 1. 回答质量指标

- 准确性：回答是否基于文档内容
- 完整性：是否回答了问题的所有方面
- 相关性：回答是否与问题相关
- 可读性：回答是否清晰易懂

### 2. 测试方法

```python
def test_prompt_effectiveness(self):
    """测试提示词效果"""
    test_questions = [
        "学校有哪些专业？",
        "学费是多少？",
        "宿舍条件怎么样？"
    ]
    
    for question in test_questions:
        response = self.query(question)
        # 评估回答质量
        self.evaluate_response(question, response)
```

## 🛠️ 实际操作步骤

### 1. 修改提示词

编辑 `backend/app/rag_service.py` 中的 `_setup_custom_prompts()` 方法

### 2. 调整配置参数

编辑 `backend/config/settings.py` 或环境变量文件

### 3. 重启服务

```bash
# 重启主API服务
uvicorn main:app --host 0.0.0.0 --port 9000 --reload
```

### 4. 测试效果

通过前端界面或API接口测试新的提示词效果

## 📝 注意事项

1. **提示词长度**：过长的提示词可能影响性能
2. **上下文窗口**：确保提示词+文档内容不超过模型的上下文窗口
3. **一致性**：保持提示词风格的一致性
4. **测试验证**：每次修改后都要进行充分测试

## 🔗 相关文档

- [LlamaIndex提示词文档](https://docs.llamaindex.ai/en/stable/module_guides/querying/prompts/)
- [OpenAI模型参数说明](https://platform.openai.com/docs/api-reference/completions)
