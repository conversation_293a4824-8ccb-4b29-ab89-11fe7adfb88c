/**
 * 文档管理页面JavaScript
 */

class DocumentManager {
  constructor() {
    this.documents = [];
    this.allDocuments = [];
    this.lastUpdate = "-";
    this.chunkCount = 0;
    this.isLoading = false;

    this.docCountEl = document.getElementById("docCount");
    this.displayedCountEl = document.getElementById("displayedCount");
    this.chunkCountEl = document.getElementById("chunkCount");
    this.refreshBtn = document.getElementById("refreshBtn");
    this.addBtn = document.getElementById("addBtn");
    this.deleteBtn = document.getElementById("deleteBtn");
    this.uploadSection = document.getElementById("uploadSection");
    this.fileInput = document.getElementById("fileInput");
    this.searchInput = document.getElementById("searchInput");
    this.documentsContent = document.getElementById("documentsContent");

    this.init();
  }

  init() {
    this.bindEvents();
    this.loadDocuments();
  }

  bindEvents() {
    // 文件选择
    this.fileInput.addEventListener("change", (e) => this.handleFileSelect(e));

    // 刷新按钮
    this.refreshBtn.addEventListener("click", () => this.loadDocuments());

    // 增加文档按钮
    this.addBtn.addEventListener("click", () => {
      this.showConfirmModal(
        "确认更新数据库",
        `
        <div style="text-align:center;padding:1.5rem 1rem;">
          <div style="font-size:2.5rem;color:#6366f1;margin-bottom:1rem;">
            <i class="fas fa-database"></i>
          </div>
          <div style="font-size:1.1rem;margin-bottom:1rem;">
            <strong>确定要更新数据库吗？</strong>
          </div>
          <ul style="text-align:left;display:inline-block;margin:0 auto 1rem auto;padding-left:1.2em;color:#374151;">
            <li>从 <b>CMS</b> 同步新增/更新的文章到 <b>RAG数据库</b></li>
            <li>可能需要几分钟时间</li>
            <li style="color:#ef4444;"><i class="fas fa-exclamation-triangle"></i> <b>同名文章将被覆盖</b></li>
          </ul>
          <div style="color:#6b7280;font-size:0.95rem;">
            操作期间请勿关闭页面
          </div>
        </div>
        `,
        () => this.addDocuments()
      );
    });

    // 删除文档按钮
    this.deleteBtn.addEventListener("click", () => this.deleteDocuments());

    // 搜索框事件监听
    this.searchInput.addEventListener("input", () => this.filterDocuments());

    // 拖拽上传
    this.uploadSection.addEventListener("dragover", (e) => this.handleDragOver(e));
    this.uploadSection.addEventListener("dragleave", (e) => this.handleDragLeave(e));
    this.uploadSection.addEventListener("drop", (e) => this.handleDrop(e));
  }

  // 显示提示信息
  showAlert(message, type = "info") {
    const alertContainer = document.getElementById("alertContainer");
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert ${type}`;
    alertDiv.innerHTML = `
            <i class="fas fa-${
              type === "success"
                ? "check-circle"
                : type === "error"
                ? "exclamation-circle"
                : "info-circle"
            }"></i>
            ${message}
        `;
    alertDiv.style.display = "block";

    alertContainer.innerHTML = "";
    alertContainer.appendChild(alertDiv);

    // 3秒后自动隐藏
    setTimeout(() => {
      alertDiv.style.display = "none";
    }, 3000);
  }

  // 加载文档列表
  async loadDocuments() {
    try {
      this.isLoading = true;
      this.renderLoading();
      const mainApiBaseUrl = this.getMainApiBaseUrl();
      const response = await fetch(`${mainApiBaseUrl}/api/documents`);
      const data = await response.json();
      this.allDocuments = data.documents;
      this.lastUpdate = new Date().toLocaleString();
      this.filterDocuments();
    } catch (error) {
      console.error("加载文档列表失败:", error);
      this.showAlert("加载文档列表失败", "error");
      this.renderEmpty();
    } finally {
      this.isLoading = false;
    }
  }

  // 过滤并渲染文档
  filterDocuments() {
    const searchTerm = this.searchInput.value.toLowerCase();
    const filteredDocuments = this.allDocuments.filter(doc => 
      doc.filename.toLowerCase().includes(searchTerm)
    );
    this.renderDocuments(filteredDocuments);
  }

  // 渲染文档列表
  renderDocuments(documents) {
    this.updateStats(documents);
    if (documents.length === 0) {
      this.renderEmpty();
      return;
    }

    const tableHTML = `
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>文档块数</th>
                        <th>文件大小</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${documents
                      .map(
                        (doc) => `
                        <tr>
                            <td>
                                <div class="file-name">${doc.filename}</div>
                                <div class="file-stats">${doc.file_path || ''}</div>
                            </td>
                            <td>${doc.chunks_count || 0}</td>
                            <td>${this.formatFileSize(doc.file_size)}</td>
                            <td>${this.formatDate(doc.file_modified)}</td>
                            <td>
                                <button class="table-delete-btn" data-filename="${encodeURIComponent(doc.filename)}">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `
                      )
                      .join("")}
                </tbody>
            </table>
        `;

    this.documentsContent.innerHTML = tableHTML;
    this.documents = documents;
    this.bindDeleteButtons();
  }

  // 更新统计信息
  updateStats(documents) {
    document.getElementById("statsBar").style.display = "flex";
    this.docCountEl.textContent = this.allDocuments.length;
    this.displayedCountEl.textContent = documents.length;

    const totalChunks = this.allDocuments.reduce((sum, doc) => sum + (doc.chunks_count || 0), 0);
    this.chunkCountEl.textContent = totalChunks;
    document.getElementById("lastUpdate").textContent = this.lastUpdate;
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // 格式化日期
  formatDate(timestamp) {
    if (!timestamp) return "-";
    const date = new Date(parseFloat(timestamp) * 1000);
    return date.toLocaleString();
  }

  // 处理文件选择
  handleFileSelect(event) {
    const files = event.target.files;
    this.uploadFiles(files);
  }

  // 处理拖拽
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add("dragover");
  }

  handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove("dragover");
  }

  handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove("dragover");
    const files = event.dataTransfer.files;
    this.uploadFiles(files);
  }

  // 上传文件
  async uploadFiles(files) {
    for (let file of files) {
      if (!file.name.endsWith(".txt")) {
        this.showAlert(`文件 ${file.name} 不是TXT格式，已跳过`, "error");
        continue;
      }

      await this.uploadSingleFile(file);
    }

    // 重新加载文档列表
    this.loadDocuments();
  }

  // 上传单个文件
  async uploadSingleFile(file, redirect_url = null, content_url = null) {
    try {
      const mainApiBaseUrl = this.getMainApiBaseUrl();
      const formData = new FormData();
      formData.append("file", file);
      
      // 添加 URL 参数
      if (redirect_url) {
        formData.append("redirect_url", redirect_url);
      }
      if (content_url) {
        formData.append("content_url", content_url);
      }

      const response = await fetch(`${mainApiBaseUrl}/api/documents/upload`, {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        if (data.replaced) {
          this.showAlert(
            `文件 ${data.filename} 上传成功！替换了同名文件（旧: ${data.old_chunks}块 → 新: ${data.new_chunks}块）<br>已保存到 data 目录`,
            "success"
          );
        } else {
          this.showAlert(
            `文件 ${data.filename} 上传成功！生成了 ${data.new_chunks} 个文档块<br>已保存到 data 目录`,
            "success"
          );
        }
      } else {
        this.showAlert(`上传失败: ${data.message}`, "error");
      }
    } catch (error) {
      console.error("上传文件失败:", error);
      this.showAlert(`上传文件 ${file.name} 失败`, "error");
    }
  }

  // 删除文档 - 从RAG数据库中删除CMS数据库中没有的文章
  async deleteDocuments() {
    console.log("🔍 开始执行删除文档操作...");
    const deleteBtn = document.getElementById("deleteBtn");
    const originalText = deleteBtn.innerHTML;
    
    try {
      // 禁用按钮并显示加载状态
      deleteBtn.disabled = true;
      deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 删除中...';
      
      this.showAlert("正在检查需要删除的文章...", "info");
      console.log("📡 正在请求 /articles/to-delete 接口...");
      
      let deletedCount = 0;
      let errorCount = 0;
      
      // 获取需要删除的文章
      try {
        const apiBaseUrl = this.getApiBaseUrl();
        const deleteArticlesUrl = `${apiBaseUrl}/articles/to-delete`;
        
        console.log(`🌐 发送请求到: ${deleteArticlesUrl}`);
        const deleteResponse = await fetch(deleteArticlesUrl);
        console.log(`📊 响应状态码: ${deleteResponse.status}`);
        console.log(`📊 响应状态: ${deleteResponse.statusText}`);
        
        if (deleteResponse.ok) {
          const articlesToDelete = await deleteResponse.json();
          console.log(`ℹ️ 从RAG获取到 ${articlesToDelete.length} 篇待删除文章。`);
          
          if (articlesToDelete.length === 0) {
            this.showAlert("✅ 没有需要删除的文章", "success");
            return;
          }

          this.showAlert(`发现 ${articlesToDelete.length} 篇需要删除的文章`, "info");
          
          if (!confirm(`确定要删除 ${articlesToDelete.length} 篇文档吗？\n\n此操作不可恢复！`)) {
            console.log("❌ 用户取消了删除操作");
            return;
          }

          for (const article of articlesToDelete) {
            try {
              console.log(`🔍 正在处理删除文章: ${article.filename}`);
              
              if (article.filename) {
                // 删除文档（不带确认框，不刷新列表）
                console.log(`🗑️ 开始删除文档: ${article.filename}`);
                await this.deleteDocument(article.filename, false, false);
                deletedCount++;
                console.log(`✅ 成功删除文章: ${article.filename}`);
              } else {
                console.warn(`⚠️ 文章缺少filename字段:`, article);
              }
            } catch (error) {
              console.error(`❌ 删除文章失败: ${article.filename}`, error);
              errorCount++;
            }
          }
        } else {
          console.error("❌ 获取需要删除的文章失败:", deleteResponse.status);
          const errorText = await deleteResponse.text();
          console.error("❌ 错误响应内容:", errorText);
          this.showAlert(`获取需要删除的文章失败: HTTP ${deleteResponse.status}`, "error");
          return;
        }
      } catch (error) {
        console.error("❌ 连接数据管道服务失败:", error);
        console.error("❌ 错误详情:", error.message);
        this.showAlert(`连接数据管道服务失败: ${error.message}`, "error");
        return;
      }
      
      // 重新加载文档列表
      console.log("🔄 重新加载文档列表...");
      await this.loadDocuments();
      
      // 显示更新结果
      let resultMessage = `文档删除完成！`;
      if (deletedCount > 0) {
        resultMessage += ` 删除了 ${deletedCount} 篇文档`;
      }
      if (errorCount > 0) {
        resultMessage += ` (${errorCount} 个操作失败)`;
      }
      
      console.log(`🎉 删除文档操作完成: ${resultMessage}`);
      this.showAlert(resultMessage, errorCount > 0 ? "error" : "success");
      
    } catch (error) {
      console.error("❌ 删除文档失败:", error);
      console.error("❌ 错误堆栈:", error.stack);
      this.showAlert(`删除文档失败: ${error.message}`, "error");
    } finally {
      // 恢复按钮状态
      console.log("🔄 恢复按钮状态");
      deleteBtn.disabled = false;
      deleteBtn.innerHTML = originalText;
    }
  }

  // 删除文档
  async deleteDocument(filename, showConfirm = true, reload = true) {
    console.log(`🔍 开始删除文档: ${filename}`);
    
    if (showConfirm && 
      !confirm(
        `确定要删除文档 "${filename}" 吗？\n\n此操作将删除：\n- ChromaDB中的所有相关向量和文档块\n- docstore中的所有相关节点\n- 所有相关元数据\n- data目录中的文件\n\n此操作不可恢复！`
      )
    ) {
      console.log(`❌ 用户取消了删除操作: ${filename}`);
      return;
    }

    try {
      const mainApiBaseUrl = this.getMainApiBaseUrl();
      console.log(`📡 发送删除请求: ${mainApiBaseUrl}/api/documents/${encodeURIComponent(filename)}`);
      const response = await fetch(
        `${mainApiBaseUrl}/api/documents/${encodeURIComponent(filename)}`,
        {
          method: "DELETE",
        }
      );

      console.log(`📊 删除响应状态码: ${response.status}`);
      console.log(`📊 删除响应状态: ${response.statusText}`);

      const data = await response.json();
      console.log(`📋 删除响应数据:`, data);

      if (data.success) {
        let message = `文档 ${data.filename} 删除成功！删除了 ${data.deleted_chunks} 个文档块`;
        if (data.file_deleted_from_disk) {
          message += "<br>已从 data 目录删除文件";
        } else {
          message += "<br>仅删除了数据库记录";
        }
        console.log(`✅ 删除成功: ${message}`);
        this.showAlert(message, "success");
        if (reload) {
            this.loadDocuments();
        }
      } else {
        console.error(`❌ 删除失败: ${data.message}`);
        this.showAlert(`删除失败: ${data.message}`, "error");
      }
    } catch (error) {
      console.error(`❌ 删除文档失败: ${filename}`, error);
      console.error(`❌ 错误详情:`, error.message);
      this.showAlert(`删除文档 ${filename} 失败`, "error");
    }
  }

  // 增加文档 - 从CMS数据库添加文章到RAG数据库
  async addDocuments() {
    console.log("🔍 开始执行增加文档操作...");
    const addBtn = document.getElementById("addBtn");
    const originalText = addBtn.innerHTML;
    
    try {
      // 禁用按钮并显示加载状态
      addBtn.disabled = true;
      addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 增加中...';
      
      this.showAlert("正在检查需要添加的文章...", "info");
      console.log("📡 正在请求 /articles/to-add 接口...");
      
      let addedCount = 0;
      const contentFetchFailures = [];
      const otherFailures = [];
      
      // 获取需要添加的文章
      try {
        // 使用相对URL或从配置中获取基础URL
        const apiBaseUrl = this.getApiBaseUrl();
        const addArticlesUrl = `${apiBaseUrl}/articles/to-add`;
        
        console.log(`🌐 发送请求到: ${addArticlesUrl}`);
        const addResponse = await fetch(addArticlesUrl);
        console.log(`📊 响应状态码: ${addResponse.status}`);
        console.log(`📊 响应状态: ${addResponse.statusText}`);
        
        if (addResponse.ok) {
          const articlesToAdd = await addResponse.json();
          console.log(`ℹ️ 从CMS获取到 ${articlesToAdd.length} 篇待处理文章。`);
          
          if (articlesToAdd.length === 0) {
              this.showAlert("✅ 没有需要新增的文章", "success");
              return;
          }

          this.showAlert(`发现 ${articlesToAdd.length} 篇需要添加的文章，正在处理...`, "info");
          
          for (const article of articlesToAdd) {
            try {
              console.log(`🔍 正在处理文章: ${article.title} (ID: ${article.content_id})`);
              
              // 根据content_id获取文章内容
              const contentUrl = `http://localhost:9001/articles/${article.content_id}/txt`;
              console.log(`📡 请求文章内容: ${contentUrl}`);
              
              const contentResponse = await fetch(contentUrl);
              console.log(`📊 文章内容响应状态码: ${contentResponse.status}`);
              
              if (contentResponse.ok) {
                const content = await contentResponse.text();
                console.log(`📄 获取到文章内容，长度: ${content.length} 字符`);
                
                // 创建文件对象
                const filename = article.filename || `${article.title}.txt`;
                console.log(`📁 创建文件: ${filename}`);
                
                const blob = new Blob([content], { type: "text/plain" });
                const file = new File([blob], filename, { type: "text/plain" });
                
                // 检查是否为更新操作
                const isUpdate = article.action === "update" || article.chunks_count > 0;
                const operationType = isUpdate ? "🔄 更新" : "➕ 新增";
                
                // 上传文档
                console.log(`${operationType} 开始上传文件: ${filename}`);
                console.log(`🔗 redirect_url: ${article.redirect_url || 'null'}`);
                console.log(`🔗 content_url: ${article.content_url || 'null'}`);
                await this.uploadSingleFile(file, article.redirect_url, article.content_url);
                addedCount++;
                console.log(`✅ 成功${isUpdate ? '更新' : '添加'}文章: ${filename}`);
              } else {
                const errorText = await contentResponse.text();
                console.error(`❌ 获取文章内容失败: ${article.content_id}, 状态码: ${contentResponse.status}`);
                console.error(`❌ 错误响应:`, errorText);
                contentFetchFailures.push({ 
                    id: article.content_id, 
                    title: article.title, 
                    reason: `获取内容失败 (HTTP ${contentResponse.status})`,
                    error: errorText
                });
              }
            } catch (error) {
              console.error(`❌ 处理文章失败: ${article.title}`, error);
              otherFailures.push({ 
                  id: article.content_id, 
                  title: article.title, 
                  reason: '处理时发生未知错误',
                  error: error.message
              });
            }
          }
        } else {
          const errorText = await addResponse.text();
          console.error("❌ 获取需要添加的文章列表失败:", addResponse.status);
          console.error("❌ 错误响应内容:", errorText);
          this.showAlert(`获取需要添加的文章列表失败: HTTP ${addResponse.status}`, "error");
          return;
        }
      } catch (error) {
        console.error("❌ 连接数据管道服务失败:", error);
        console.error("❌ 错误详情:", error.message);
        this.showAlert(`连接数据管道服务失败: ${error.message}`, "error");
        return;
      }
      
      // 重新加载文档列表
      console.log("🔄 重新加载文档列表...");
      await this.loadDocuments();
      
      const totalFailed = contentFetchFailures.length + otherFailures.length;

      // 显示更新结果
      console.log(`--- ✨ '增加文档' 操作总结 ---`);
      console.log(`  - 待处理总数: ${addedCount + totalFailed}`);
      console.log(`  - ✅ 成功添加: ${addedCount}`);
      console.log(`  - ❌ 失败总数: ${totalFailed}`);
      
      if (contentFetchFailures.length > 0) {
        console.warn(`    - 失败 (获取内容失败): ${contentFetchFailures.length} 个`);
        for (const failed of contentFetchFailures) {
          console.warn(`      - ID: ${failed.id}, 标题: ${failed.title}`);
        }
      }
      
      if (otherFailures.length > 0) {
        console.warn(`    - 失败 (其他错误): ${otherFailures.length} 个`);
        for (const failed of otherFailures) {
          console.warn(`      - ID: ${failed.id}, 标题: ${failed.title}, 原因: ${failed.error}`);
        }
      }
      console.log(`--- 结束总结 ---`);

      let resultMessage = `文档增加完成！`;
      if (addedCount > 0) {
        resultMessage += ` 添加了 ${addedCount} 篇文档。`;
      }
      if (totalFailed > 0) {
        resultMessage += ` (${totalFailed} 个无效文章，详情请看控制台)`;
      }
      
      console.log(`🎉 增加文档操作完成: ${resultMessage}`);
      this.showAlert(resultMessage, totalFailed > 0 ? "error" : "success");
      
    } catch (error) {
      console.error("❌ 增加文档失败:", error);
      console.error("❌ 错误堆栈:", error.stack);
      this.showAlert(`增加文档失败: ${error.message}`, "error");
    } finally {
      // 恢复按钮状态
      console.log("🔄 恢复按钮状态");
      addBtn.disabled = false;
      addBtn.innerHTML = originalText;
    }
  }

  renderLoading() {
    this.documentsContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-icon"><i class="fas fa-spinner fa-spin"></i></div>
        <p>加载中...</p>
      </div>
    `;
  }

  renderEmpty() {
    const searchTerm = this.searchInput.value;
    let message = "没有已上传的文档";
    if (searchTerm) {
      message = `没有找到名为 "${searchTerm}" 的文档`;
    }
    this.documentsContent.innerHTML = `
      <div class="empty-state">
        <div class="empty-icon"><i class="fas fa-file-excel"></i></div>
        <p>${message}</p>
      </div>
    `;
  }

  bindDeleteButtons() {
    const deleteButtons = this.documentsContent.querySelectorAll(".table-delete-btn");
    deleteButtons.forEach(button => {
      button.addEventListener("click", (event) => {
        const filename = decodeURIComponent(event.currentTarget.dataset.filename);
        this.deleteDocument(filename);
      });
    });
  }

  // 显示自定义确认弹窗
  showConfirmModal(title, content, onConfirm) {
    const confirmModal = document.getElementById("confirmModal");
    const confirmTitle = document.getElementById("confirmTitle");
    const confirmContent = document.getElementById("confirmContent");
    const confirmCancel = document.getElementById("confirmCancel");
    const confirmOk = document.getElementById("confirmOk");

    confirmTitle.textContent = title;
    confirmContent.innerHTML = content;
    confirmModal.classList.remove("hidden");

    // 解绑旧事件，绑定新事件
    const newConfirmOk = confirmOk.cloneNode(true);
    confirmOk.parentNode.replaceChild(newConfirmOk, confirmOk);

    newConfirmOk.addEventListener("click", () => {
      confirmModal.classList.add("hidden");
      if (onConfirm) onConfirm();
    });

    confirmCancel.addEventListener("click", () => {
      confirmModal.classList.add("hidden");
    });
  }

  // 获取API基础URL的方法
  getApiBaseUrl() {
    // 1. 首先尝试从页面中的配置元素获取
    const configElement = document.getElementById('api-config');
    if (configElement && configElement.dataset.apiBaseUrl) {
      return configElement.dataset.apiBaseUrl;
    }
    
    // 2. 如果没有配置元素，则根据当前环境动态确定
    const currentHost = window.location.hostname;
    
    // 如果是本地开发环境
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
      return 'http://localhost:9001'; // 本地环境使用9001端口
    }
    
    // 如果是生产环境，使用9001端口
    return `${window.location.protocol}//${currentHost}:9001`;
    
    // 或者使用相对路径（如果API和前端在同一域名下的不同路径）
    // return '/api/data-pipeline';
  }

  // 获取主API服务的基础URL
  getMainApiBaseUrl() {
    // 1. 首先尝试从页面中的配置元素获取
    const configElement = document.getElementById('api-config');
    if (configElement && configElement.dataset.mainApiUrl) {
      return configElement.dataset.mainApiUrl;
    }
    
    // 2. 如果没有配置元素，则根据当前环境动态确定
    const currentHost = window.location.hostname;
    
    // 如果是本地开发环境
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
      return 'http://localhost:9000'; // 本地环境使用9000端口
    }
    
    // 如果是生产环境，使用9000端口
    return `${window.location.protocol}//${currentHost}:9000`;
  }
}

// 初始化文档管理器
const documentManager = new DocumentManager();
