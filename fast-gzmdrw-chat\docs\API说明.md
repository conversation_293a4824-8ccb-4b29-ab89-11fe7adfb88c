# API接口分离指南

## 🎯 设计原则

本项目采用清晰的逻辑分离设计，确保数据管道和RAG服务的职责明确，便于维护和扩展。

## 📊 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   外部系统      │    │   数据管道      │
│  (Frontend)     │    │  (External)     │    │ (Data Pipeline) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    RAG主服务 (main.py)                          │
│                   端口: 9000/9000                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                  RAG核心服务 (rag_service.py)                   │
│                    文档处理和查询引擎                            │
└─────────────────────────────────────────────────────────────────┘
```

## 🔌 API接口分类

### 1. 前端交互API (主要用于Web界面)

**基础路径**: `/api/`
**端口**: 9000 (开发) / 9000 (生产)

| 接口 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/api/status` | GET | 获取系统状态 | 前端状态显示 |
| `/api/query` | POST | 智能问答查询 | 前端聊天功能 |
| `/api/load-documents` | POST | 更新数据库 | 前端文档管理 |
| `/api/documents` | GET | 获取文档列表 | 前端文档展示 |
| `/api/documents/{filename}` | DELETE | 删除文档 | 前端文档管理 |

### 2. 调试API (开发和运维使用)

**基础路径**: `/api/debug/`
**端口**: 9000 (开发) / 9000 (生产)

| 接口 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/api/debug/info` | GET | 获取调试信息 | 系统诊断 |
| `/api/debug/database` | GET | 数据库统计 | 性能监控 |
| `/api/debug/search` | POST | 调试搜索 | 搜索优化 |
| `/api/debug/clear-cache` | POST | 清理缓存 | 性能维护 |
| `/api/debug/health` | GET | 健康检查 | 服务监控 |

### 3. 外部调用API (第三方系统集成)

**基础路径**: `/api/external/`
**端口**: 9000 (开发) / 9000 (生产)

| 接口 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/api/external/query` | POST | 外部查询接口 | 第三方系统调用 |
| `/api/external/status` | GET | 外部状态检查 | 第三方系统监控 |
| `/api/external/documents` | GET | 外部文档列表 | 第三方系统集成 |

### 4. 数据管道API (CMS数据同步)

**基础路径**: `/`
**端口**: 9001 (开发) / 9001 (生产)

| 接口 | 方法 | 描述 | 用途 |
|------|------|------|------|
| `/articles/summary` | GET | 文章摘要列表 | CMS数据同步 |
| `/articles/to-add` | GET | 待添加文章 | CMS数据同步 |
| `/articles/to-delete` | GET | 待删除文章 | CMS数据同步 |
| `/articles/{id}/txt` | GET | 文章文本内容 | CMS数据获取 |
| `/debug/rag-connection` | GET | RAG连接测试 | 系统诊断 |

## 🏗️ 逻辑分离设计

### RAG主服务 (main.py)
**职责**:
- API路由定义和请求处理
- 请求验证和响应格式化
- 错误处理和日志记录
- 跨域配置和中间件

**不负责**:
- 具体的文档处理逻辑
- 向量化和检索算法
- 数据库直接操作

### RAG核心服务 (rag_service.py)
**职责**:
- 文档加载和处理
- 向量化和索引构建
- 查询引擎和检索逻辑
- ChromaDB操作

**不负责**:
- HTTP请求处理
- API响应格式化
- 前端交互逻辑

### 数据管道服务 (data_pipeline.py)
**职责**:
- CMS数据库连接和查询
- 文章数据格式转换
- 数据同步逻辑
- 文件下载和处理

**不负责**:
- RAG查询处理
- 向量化操作
- 前端界面逻辑

## 📝 API使用示例

### 1. 前端查询示例
```javascript
// 前端聊天查询
const response = await fetch('/api/query', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        query: "用户问题",
        max_results: 5
    })
});
```

### 2. 外部系统调用示例
```python
# 第三方系统调用
import requests

response = requests.post('http://your-server:9000/api/external/query', {
    "question": "查询问题",
    "max_sources": 5,
    "include_content": True,
    "similarity_threshold": 0.7
})

result = response.json()
print(f"答案: {result['answer']}")
print(f"来源: {len(result['sources'])} 个文档")
```

### 3. 调试接口示例
```bash
# 系统健康检查
curl http://localhost:9000/api/debug/health

# 获取数据库统计
curl http://localhost:9000/api/debug/database

# 调试搜索功能
curl -X POST http://localhost:9000/api/debug/search \
  -H "Content-Type: application/json" \
  -d '{"query": "测试查询", "max_results": 3}'
```

## 🔒 安全考虑

### 接口访问控制
- **前端API**: 允许跨域访问，适合Web界面调用
- **调试API**: 建议生产环境限制访问
- **外部API**: 可添加API密钥验证
- **数据管道API**: 内部服务调用，限制外部访问

### 数据隔离
- RAG服务只处理文档查询，不直接访问CMS数据库
- 数据管道服务只处理数据同步，不处理查询逻辑
- 各服务独立配置，避免配置混乱

## 🚀 扩展建议

### 1. 添加认证中间件
```python
# 为外部API添加认证
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    if request.url.path.startswith("/api/external/"):
        # 验证API密钥
        api_key = request.headers.get("X-API-Key")
        if not validate_api_key(api_key):
            return JSONResponse(
                status_code=401, 
                content={"error": "Invalid API key"}
            )
    return await call_next(request)
```

### 2. 添加限流控制
```python
# 为外部API添加限流
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/external/query")
@limiter.limit("10/minute")
async def external_query(request: Request, ...):
    # 查询逻辑
    pass
```

### 3. 添加监控指标
```python
# 添加性能监控
import time
from prometheus_client import Counter, Histogram

query_counter = Counter('rag_queries_total', 'Total queries')
query_duration = Histogram('rag_query_duration_seconds', 'Query duration')

@app.post("/api/query")
async def query_documents(request: QueryRequest):
    start_time = time.time()
    query_counter.inc()
    
    try:
        # 查询逻辑
        result = rag_service.query(request.query)
        return result
    finally:
        query_duration.observe(time.time() - start_time)
```

## 📋 维护清单

### 定期检查
- [ ] API响应时间监控
- [ ] 错误率统计
- [ ] 资源使用情况
- [ ] 日志文件大小

### 代码质量
- [ ] 接口文档更新
- [ ] 单元测试覆盖
- [ ] 代码注释完整
- [ ] 错误处理完善

### 安全审查
- [ ] 敏感信息泄露检查
- [ ] 输入验证完整性
- [ ] 访问控制有效性
- [ ] 日志安全性

---

通过这种清晰的分离设计，确保了系统的可维护性、可扩展性和安全性。
